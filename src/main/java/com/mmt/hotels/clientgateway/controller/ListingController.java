package com.mmt.hotels.clientgateway.controller;

import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersResponse;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.FetchCollectionResponseV2;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.response.wrapper.ResponseWrapper;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.MetricAspect;
import com.mmt.hotels.clientgateway.util.RequestHandler;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.util.Tuple;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Date;
import java.util.Map;
import org.apache.commons.collections.CollectionUtils;
import com.mmt.hotels.clientgateway.response.wrapper.Error;

import static com.mmt.hotels.clientgateway.constants.Constants.CK_CORRELATION_KEY;
import static com.mmt.hotels.clientgateway.constants.Constants.REQUEST_IDENTIFIER;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.*;

@RestController
@RequestMapping("/")
public class ListingController {
	
	@Autowired
	private RequestHandler requestHandler;
	
	@Autowired
	private ListingService listingService;

	@Autowired
	private MetricAspect metricAspect;

	@Autowired
	private Utility utility;

	@RequestMapping(value = "cg/search-hotels/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchHotelsResponse>> searchHotels(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchHotelsRequest searchHotelsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
					throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchHotelsRequest.getRequestDetails().getIdContext() ,LISTING_SEARCH_HOTELS,searchHotelsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchHotelsRequest, correlationKey);
		ResponseWrapper<SearchHotelsResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		Map<String, String[]> parameterMap = utility.addSrcReqToParameterMap(httpServletRequest.getParameterMap());
		searchHotelsResponseWrapper.setResponse(listingService.searchHotels(searchHotelsRequest, parameterMap, tup.getY()));
		searchHotelsResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/search_hotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/fetchCollections/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FetchCollectionResponse>> fetchCollection(@Valid @RequestBody FetchCollectionRequest fetchCollectionRequest,
																					@PathVariable("client") String client,
																					@PathVariable("version") String version,
																					HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
																					@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
																					@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, fetchCollectionRequest.getRequestDetails().getIdContext(), FETCH_COLLECTIONS, fetchCollectionRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, fetchCollectionRequest, correlationKey);
		ResponseWrapper<FetchCollectionResponse>  fetchCollectionResponse=new ResponseWrapper<>();
		fetchCollectionResponse.setResponse(listingService.fetchCollections(fetchCollectionRequest, httpServletRequest.getParameterMap(),tup.getY()));
		fetchCollectionResponse.setCorrelationKey(correlationKey);
		MDC.clear();
		return new ResponseEntity<>(fetchCollectionResponse, HttpStatus.OK);
	}


	@RequestMapping(value = "/cg/v2.0/fetchCollections/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FetchCollectionResponseV2>> fetchCollectionV2(@Valid @RequestBody FetchCollectionRequest fetchCollectionRequest,
																					@PathVariable("client") String client,
																					@PathVariable("version") String version,
																					HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
																					@RequestParam(name = Constants.CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
																						@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId) throws ClientGatewayException {
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String, Map<String, String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, fetchCollectionRequest.getRequestDetails().getIdContext(), FETCH_COLLECTIONS, fetchCollectionRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, fetchCollectionRequest, correlationKey);
		ResponseWrapper<FetchCollectionResponseV2>  fetchCollectionResponse = new ResponseWrapper<>();
		fetchCollectionResponse.setResponse(listingService.fetchCollectionsV2(fetchCollectionRequest, httpServletRequest.getParameterMap(),tup.getY()));
		fetchCollectionResponse.setCorrelationKey(correlationKey);
		MDC.clear();
		return new ResponseEntity<>(fetchCollectionResponse, HttpStatus.OK);
	}


	@RequestMapping(value = "cg/mob-landing/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<MobLandingResponse>> mobLanding(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody MobLandingRequest mobLandingRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
	throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		SearchHotelsCriteria searchHotelsCriteria = mobLandingRequest.getSearchCriteria();
		if (searchHotelsCriteria != null){
			searchHotelsCriteria.setPersonalizedSearch(true);
		}
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client, mobLandingRequest.getRequestDetails().getIdContext()  , LISTING_MOB_LANDING,mobLandingRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, mobLandingRequest, correlationKey);
		ResponseWrapper<MobLandingResponse> mobLandingResponseWrapper = new ResponseWrapper<>();
		mobLandingResponseWrapper.setResponse(listingService.mobLanding(mobLandingRequest, httpServletRequest.getParameterMap(), tup.getY()));
		mobLandingResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/mob_landing",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));

		MDC.clear();
		return new ResponseEntity<>(mobLandingResponseWrapper, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/filter-count/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FilterResponse>> filterCount(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody FilterCountRequest filterCountRequest, @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				 filterCountRequest.getRequestDetails().getIdContext()  , LISTING_FILTER_COUNT,filterCountRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, filterCountRequest, correlationKey);
		ResponseWrapper<FilterResponse> filterCountResponse = new ResponseWrapper<>();
		filterCountResponse.setResponse(listingService.filterCount(filterCountRequest, httpServletRequest.getParameterMap(), tup.getY(), seoCorp));
		filterCountResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/filter_count",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(filterCountResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/batch-filters/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<FilterResponse>> batchFilter(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody FilterCountRequest batchFilterRequest, @RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				 batchFilterRequest.getRequestDetails().getIdContext()  , LISTING_BATCH_FILTER,batchFilterRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, batchFilterRequest, correlationKey);
		ResponseWrapper<FilterResponse> filterCountResponse = new ResponseWrapper<>();
		filterCountResponse.setResponse(listingService.batchFilterResponse(batchFilterRequest, tup.getY(), seoCorp));
		filterCountResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/batch_filters",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(filterCountResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/smart-filters/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SmartFiltersResponse>> smartFilter(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody FilterCountRequest filterCountRequest, 
			@RequestParam(name = "seoCorp", required = false) boolean seoCorp,
			HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException {
		
		long startTime = new Date().getTime();
		client = client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup = requestHandler.handleCommonRequest(httpServletRequest, httpServletResponse, ck, client, filterCountRequest.getRequestDetails().getIdContext(), LISTING_FILTER_COUNT, filterCountRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, filterCountRequest, correlationKey);
		
		// Service call - business logic moved here
		SmartFiltersResponse smartFiltersResponse = listingService.getSmartFilters(filterCountRequest, httpServletRequest.getParameterMap(), tup.getY(), seoCorp);
		
		// Response building
		ResponseWrapper<SmartFiltersResponse> filtersResponse = new ResponseWrapper<>();
		
		if (CollectionUtils.isNotEmpty(smartFiltersResponse.getFilters())) {
			filtersResponse.setResponse(smartFiltersResponse);
		} else {
			filtersResponse.setError(new Error("200", "No Filters Found"));
		}
		filtersResponse.setCorrelationKey(correlationKey);
		
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/smart-filters", MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(filtersResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/listing-map/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<ListingMapResponse>> listingMap(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody ListingMapRequest listingMapRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				 listingMapRequest.getRequestDetails().getIdContext() ,LISTING_MAP,listingMapRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, listingMapRequest, correlationKey);
		ResponseWrapper<ListingMapResponse> listingMapResponse = new ResponseWrapper<>();
		listingMapResponse.setResponse(listingService.listingMap(listingMapRequest, httpServletRequest.getParameterMap(), tup.getY()));
		listingMapResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/listing_map",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(listingMapResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/groupBooking/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<GroupBookingResponse>> groupBooking(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody GroupBookingRequest groupBookingRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,
				Constants.B2C ,GROUP_BOOKING,groupBookingRequest);
		String correlationKey = tup.getX();
		groupBookingRequest.setCorrelationKey(correlationKey);
		ResponseWrapper<GroupBookingResponse> groupBookingResponse = new ResponseWrapper<>();
		groupBookingResponse.setResponse(listingService.submitGroupBooking(groupBookingRequest));
		groupBookingResponse.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, GROUP_BOOKING,MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(groupBookingResponse, HttpStatus.OK);
	}

	@RequestMapping(value = "cg/wishListed-hotels/{client}/{version}", method = RequestMethod.POST)
	public ResponseEntity<ResponseWrapper<SearchHotelsResponse>> wishListedHotels(
			@PathVariable("client") String client, @PathVariable("version") String version,
			@Valid @RequestBody SearchHotelsRequest searchHotelsRequest, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
			@RequestParam(name = CK_CORRELATION_KEY, required = false, defaultValue = Constants.EMPTY_STRING) String ck,
			@RequestParam(name = REQUEST_IDENTIFIER, required = false, defaultValue = Constants.EMPTY_STRING) String requestId)
			throws ClientGatewayException{
		long startTime = new Date().getTime();
		client=client.toUpperCase();
		ck = requestHandler.effectiveCorrelationKey(requestId, ck);
		Tuple<String,Map<String,String>> tup= requestHandler.handleCommonRequest(httpServletRequest,httpServletResponse,ck,client,searchHotelsRequest.getRequestDetails().getIdContext(), WISHLISTED_HOTELS, searchHotelsRequest);
		String correlationKey = tup.getX();
		requestHandler.validatHeadersAndCreateMDC(httpServletRequest, client, searchHotelsRequest, correlationKey);
		ResponseWrapper<SearchHotelsResponse> searchHotelsResponseWrapper = new ResponseWrapper<>();
		searchHotelsResponseWrapper.setResponse(listingService.searchHotels(searchHotelsRequest, httpServletRequest.getParameterMap(), tup.getY()));
		searchHotelsResponseWrapper.setCorrelationKey(correlationKey);
		metricAspect.addToTime(new Date().getTime() - startTime, "cg/wishListed-hotels",MDC.get(MDCHelper.MDCKeys.COUNTRY.getStringValue()), MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()), client, MDC.get(MDCHelper.MDCKeys.MDC_SRC_CHANNEL.getStringValue()));
		MDC.clear();
		return new ResponseEntity<>(searchHotelsResponseWrapper, HttpStatus.OK);
	}

}