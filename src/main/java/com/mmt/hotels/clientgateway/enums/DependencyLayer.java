package com.mmt.hotels.clientgateway.enums;

import lombok.Getter;

@Getter
public enum DependencyLayer {
	
	CLIENTGATEWAY("00"),
	CLIENTS("01"),
	CLIENTBACKEND("02"),
	ORCHESTRATOR("03"),
	POKUS("04"),
	USERSERVICE("05"),
	HYDRA("06"),
	CORPORATE("07"),
	FLYFISH("08"),
	POLYGLOT("09"),
	BABELFISH("10"),
	HERMES("11"),
	HSC("12"),
	DPT("13"),
	PFM("14"),
	ORCHESTRATOR_NEW("15"),
	ORCHESTRATOR_DETAIL("16"),
	CHATBOT("17");

	private String code;
	
	private DependencyLayer(String code) {
		this.code = code;
	}
}
