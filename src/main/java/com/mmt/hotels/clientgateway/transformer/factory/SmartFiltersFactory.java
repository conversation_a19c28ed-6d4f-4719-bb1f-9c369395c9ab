package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.SmartFiltersRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SmartFiltersRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SmartFiltersRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SmartFiltersRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SmartFiltersRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SmartFiltersResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SmartFiltersResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SmartFiltersResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SmartFiltersResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SmartFiltersResponseTransformerPWA;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SmartFiltersFactory {

    @Autowired
    private SmartFiltersRequestTransformerPWA smartFiltersRequestTransformerPWA;

    @Autowired
    private SmartFiltersRequestTransformerAndroid smartFiltersRequestTransformerAndroid;

    @Autowired
    private SmartFiltersRequestTransformerDesktop smartFiltersRequestTransformerDesktop;

    @Autowired
    private SmartFiltersRequestTransformerIOS smartFiltersRequestTransformerIOS;

    @Autowired
    private SmartFiltersResponseTransformerPWA smartFiltersResponseTransformerPWA;

    @Autowired
    private SmartFiltersResponseTransformerAndroid smartFiltersResponseTransformerAndroid;

    @Autowired
    private SmartFiltersResponseTransformerDesktop smartFiltersResponseTransformerDesktop;

    @Autowired
    private SmartFiltersResponseTransformerIOS smartFiltersResponseTransformerIOS;

    public SmartFiltersRequestTransformer getRequestService(String client) {
        if (StringUtils.isEmpty(client))
            return smartFiltersRequestTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return smartFiltersRequestTransformerPWA;
            case "DESKTOP": 
                return smartFiltersRequestTransformerDesktop;
            case "ANDROID": 
                return smartFiltersRequestTransformerAndroid;
            case "IOS": 
                return smartFiltersRequestTransformerIOS;
        }
        return smartFiltersRequestTransformerDesktop;
    }

    public SmartFiltersResponseTransformer getResponseService(String client) {
        if (StringUtils.isEmpty(client))
            return smartFiltersResponseTransformerDesktop;
        switch(client) {
            case "PWA":
            case "MSITE":
                return smartFiltersResponseTransformerPWA;
            case "DESKTOP": 
                return smartFiltersResponseTransformerDesktop;
            case "ANDROID": 
                return smartFiltersResponseTransformerAndroid;
            case "IOS": 
                return smartFiltersResponseTransformerIOS;
        }
        return smartFiltersResponseTransformerDesktop;
    }
}
