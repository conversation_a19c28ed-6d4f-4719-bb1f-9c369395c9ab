package com.mmt.hotels.clientgateway.transformer.response.pwa;

import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.transformer.response.SmartFiltersResponseTransformer;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SmartFiltersResponseTransformerPWA extends SmartFiltersResponseTransformer {
    
    @Override
    protected List<com.mmt.hotels.clientgateway.response.filter.Filter> applyClientSpecificTransformations(
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters,
            FilterCountRequest filterCountRequest) {
        
        // PWA-specific transformations can be added here
        // For now, just return the filters as-is
        return matchingFilters;
    }
}
