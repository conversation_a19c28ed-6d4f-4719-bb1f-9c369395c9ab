package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.enums.RatingCategory;
import com.gommt.hotels.orchestrator.detail.model.response.content.*;
import com.gommt.hotels.orchestrator.detail.model.response.content.PropertyChain;
import com.gommt.hotels.orchestrator.detail.model.response.content.PropertyChainDetails;
import com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.RatingData;
import com.gi.hotels.model.response.staticdata.FoodDining;
import com.gi.hotels.model.response.staticdata.FoodPointers;
import com.gi.hotels.model.response.staticdata.FoodRules;
import com.gi.hotels.model.response.staticdata.Gallery;
import com.gi.hotels.model.response.staticdata.Info;
import com.gi.hotels.model.response.staticdata.Meal;
import com.gi.hotels.model.response.staticdata.RestaurantDetails;
import com.gi.hotels.model.response.staticdata.Timings;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.request.DeviceDetails;
import com.mmt.hotels.clientgateway.request.StaticDetailCriteria;
import com.mmt.hotels.clientgateway.request.StaticDetailRequest;
import com.mmt.hotels.clientgateway.response.Address;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.rooms.SharedInfo;
import com.mmt.hotels.clientgateway.response.rooms.SpaceData;
import com.mmt.hotels.clientgateway.response.searchHotels.CalendarCriteria;
import com.mmt.hotels.clientgateway.response.staticdetail.*;
import com.mmt.hotels.clientgateway.response.staticdetail.ChildExtraBedPolicy;
import com.mmt.hotels.clientgateway.response.staticdetail.CommonRules;
import com.mmt.hotels.clientgateway.response.staticdetail.ExtraBedRules;
import com.mmt.hotels.clientgateway.response.staticdetail.GovtPolicies;
import com.mmt.hotels.clientgateway.response.staticdetail.HotelResult;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRules;
import com.mmt.hotels.clientgateway.response.staticdetail.HouseRulesV2;
import com.mmt.hotels.clientgateway.response.staticdetail.PolicyRules;
import com.mmt.hotels.clientgateway.response.staticdetail.Rule;
import com.mmt.hotels.clientgateway.response.staticdetail.StreetViewInfo;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.ReArchUtility;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.pojo.FoodAndDining.FoodAndDiningEnums;
import com.mmt.model.UGCRatingData;
import com.mmt.model.util.RatingDetail;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.COUPLE_FRIENDLY;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;

public abstract class HotelResultHelper {


    @Autowired
    private ReArchUtility utility;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private CommonResponseTransformer commonResponseTransformer;


    @Value("#{'${suppressed.houseRules.list}'.split(',')}")
    private List<Integer> supressedHouseRulesList;

    @Value("${value.stay.icon}")
    private String iconUrl;

    @Value("${value.stay.icon.gcc}")
    private String iconUrlGcc;

    @Value("${food.dining.min.count.config}")
    private int foodDiningMinCountConfig;

    @Value("${food.menu.position.config}")
    private int foodMenuPosition;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Value("${streetViewIconUrl}")
    private String streetViewIconUrl;

    @Value("${streetViewLocationImageUrl}")
    private String streetViewLocationImageUrl;
    
    /**
     * Platform-specific abstract methods that concrete implementations must provide
     */
    protected abstract Map<String, String> buildCardTitleMap();
    
    protected abstract void addTitleData(HotelResult hotelResult, String countryCode);
    
    protected abstract String getLuxeIcon();
    
    public abstract StaffInfo convertStaffInfo(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo);





    public HotelResult getHotelResult(StaticDetailRequest staticDetailRequestBody, HotelMetaData hotelMetaData,
                                       String context, Map<String, String> expDataMap, boolean isGroupBookingFunnel, String funnel) {

        boolean isLiteResponse = staticDetailRequestBody != null && staticDetailRequestBody.getFeatureFlags() != null && staticDetailRequestBody.getFeatureFlags().isLiteResponse();
        boolean isDisableHostChat = false;

        HotelResult hotelResult = new HotelResult();
        boolean dhCall = StringUtils.isNotEmpty(hotelMetaData.getLocationInfo().getCountryCode()) && DOM_COUNTRY.equalsIgnoreCase(hotelMetaData.getLocationInfo().getCountryCode());

//        if (hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getPropertyChain() != null && MapUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getPropertyChain().getSummary())) {
//            hotelResult.setPropertyChain(getPropertyChain(hotelMetaData.getPropertyDetails().getPropertyChain()));
//        }
        if (hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getPropertyHighlights() != null && CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getPropertyHighlights().getDetails())) {
            PropertyHighlightCG propertyHighlightCG = getPropertyHighLights(hotelMetaData.getPropertyDetails().getPropertyHighlights());
           hotelResult.setPropertyHighlights(propertyHighlightCG);
        }
        hotelResult.setGroupBookingHotel(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isGroupBookingAllowed());
        if (hotelMetaData.getLocationInfo() != null) {
            Address address = new Address();
            List<String> addressLines = hotelMetaData.getLocationInfo().getAddressLines();
            if (addressLines != null && !addressLines.isEmpty()) {
                address.setLine1(addressLines.get(0));
                if (addressLines.size() > 1) {
                    address.setLine2(addressLines.get(1));
                }
            }
            hotelResult.setAddress(address);
            if (hotelMetaData.getLocationInfo().getStreetViewInfo() != null) {
                StreetViewInfo streetViewInfo = new StreetViewInfo();
                LatLong latLong = new LatLong();
                latLong.setLat(hotelMetaData.getLocationInfo().getStreetViewInfo().getLatitude());
                latLong.setLng(hotelMetaData.getLocationInfo().getStreetViewInfo().getLongitude());
                streetViewInfo.setPanoId(hotelMetaData.getLocationInfo().getStreetViewInfo().getPanoId());
                streetViewInfo.setLatLong(latLong);
                LocationCard locCard = new LocationCard();
                locCard.setStreetViewTitle(polyglotService.getTranslatedData(STREET_VIEW));
                locCard.setLocationImageUrl(streetViewLocationImageUrl);
                streetViewInfo.setLocationCard(locCard);

                StreetInfoCard streetInfoCard = new StreetInfoCard();
                streetInfoCard.setStreetViewTitle(polyglotService.getTranslatedData(STREET_VIEW));
                streetInfoCard.setStreetViewText(polyglotService.getTranslatedData(STREET_VIEW_TEXT));
                streetInfoCard.setStreetViewIconUrl(streetViewIconUrl);
                streetViewInfo.setStreetInfoCard(streetInfoCard);
                hotelResult.setStreetViewDataAvailable(true);
                hotelResult.setStreetViewInfo(streetViewInfo);
            }
        }
        // Map PremiumUsp from PropertyHighlights
        if (hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getPremiumUsp() != null) {
            hotelResult.setPremiumUsp(mapPremiumUsp(hotelMetaData.getPropertyDetails().getPremiumUsp()));
        }
        if (hotelMetaData.getMediaContent() != null && hotelMetaData.getMediaContent().getMapUrl() != null && !hotelMetaData.getMediaContent().getMapUrl().isEmpty()) {
            hotelResult.setMapImageUrl(hotelMetaData.getMediaContent().getMapUrl());
        }
        if(hotelMetaData.getLocationInfo() != null) {
            hotelResult.setPrimaryArea(hotelMetaData.getLocationInfo().getPrimaryArea());
            hotelResult.setLat(hotelMetaData.getLocationInfo().getLatitude());
            hotelResult.setLng(hotelMetaData.getLocationInfo().getLongitude());
            hotelResult.setPinCode(hotelMetaData.getLocationInfo().getPinCode());
            hotelResult.setLocationDetail(buildLocationDetail(hotelMetaData.getLocationInfo().getLocationId(), hotelMetaData.getLocationInfo().getLocationName(),
                    hotelMetaData.getLocationInfo().getCountryCode(), hotelMetaData.getLocationInfo().getCountryName()));
        }
        hotelResult.setAltAcco(hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isAltAcco());
        if(Objects.nonNull(hotelMetaData.getPropertyDetails()) && CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories())) {
            hotelResult.setCategories(new ArrayList<>(hotelMetaData.getPropertyDetails().getCategories()));
        }
        hotelResult.setCheckinTime(hotelMetaData.getCheckInOutInfo() != null ? hotelMetaData.getCheckInOutInfo().getCheckInTime() : null);
        hotelResult.setCheckoutTime(hotelMetaData.getCheckInOutInfo() != null ? hotelMetaData.getCheckInOutInfo().getCheckOutTime() : null);
        
        // Apply business logic from StaticDetailResponseTransformer for check-in/check-out time ranges
        if (hotelMetaData.getCheckInOutInfo() != null) {
            String checkInTime = hotelMetaData.getCheckInOutInfo().getCheckInTime();
            String checkOutTime = hotelMetaData.getCheckInOutInfo().getCheckOutTime();
            String checkInTimeRange = hotelMetaData.getCheckInOutInfo().getCheckInTimeRange();
            String checkOutTimeRange = hotelMetaData.getCheckInOutInfo().getCheckOutTimeRange();
            
            // Only set time ranges if they differ from the regular times (business logic from StaticDetailResponseTransformer)
            if (checkInTime != null && checkInTimeRange != null && !checkInTime.equalsIgnoreCase(checkInTimeRange)) {
                hotelResult.setCheckinTimeRange(checkInTimeRange);
            }
            if (checkOutTime != null && checkOutTimeRange != null && !checkOutTime.equalsIgnoreCase(checkOutTimeRange)) {
                hotelResult.setCheckoutTimeRange(checkOutTimeRange);
            }
            
            hotelResult.setCheckInTimeInRange(hotelMetaData.getCheckInOutInfo().isCheckInTimeInRange());
            hotelResult.setCheckOutTimeInRange(hotelMetaData.getCheckInOutInfo().isCheckOutTimeInRange());
        }
        
        // Map check-in/check-out time ranges if available and different from regular times
        if (hotelMetaData.getCheckInOutInfo() != null) {
            String checkInTimeRange = hotelMetaData.getCheckInOutInfo().getCheckInTimeRange();
            String checkOutTimeRange = hotelMetaData.getCheckInOutInfo().getCheckOutTimeRange();
            if (StringUtils.isNotEmpty(checkInTimeRange) && !checkInTimeRange.equals(hotelMetaData.getCheckInOutInfo().getCheckInTime())) {
                hotelResult.setCheckinTimeRange(checkInTimeRange);
            }
            if (StringUtils.isNotEmpty(checkOutTimeRange) && !checkOutTimeRange.equals(hotelMetaData.getCheckInOutInfo().getCheckOutTime())) {
                hotelResult.setCheckoutTimeRange(checkOutTimeRange);
            }
        }
        hotelResult.setHotelIcon(hotelMetaData.getPropertyDetails().getHotelIcon());
        hotelResult.setId(hotelMetaData.getPropertyDetails().getId());
        hotelResult.setGiHotelId(hotelMetaData.getPropertyDetails().getGiHotelId());
        hotelResult.setLongDesc(hotelMetaData.getPropertyDetails().getLongDescription());
        hotelResult.setName(hotelMetaData.getPropertyDetails().getName());
        hotelResult.setMaskedPropertyName(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isMaskedPropertyName());
        hotelResult.setShowCallToBook(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isShowCallToBook());
        hotelResult.setPropertyType(hotelMetaData.getPropertyDetails().getPropertyType());
        hotelResult.setPropertyLabel(hotelMetaData.getPropertyDetails().getPropertyLabel());
        hotelResult.setShortDesc(hotelMetaData.getPropertyDetails().getShortDescription());
        hotelResult.setStarRating(hotelMetaData.getPropertyDetails().getStarRating());
        hotelResult.setStarRatingType(hotelMetaData.getPropertyDetails().getStarRatingType());
        hotelResult.setHighSellingAltAcco(hotelMetaData.getPropertyFlags() != null && hotelMetaData.getPropertyFlags().isHighSellingAltAcco());
        hotelResult.setStayType(hotelMetaData.getPropertyDetails().getStayType());
        
        // Apply business logic from StaticDetailResponseTransformer for entire property detection
        if (StringUtils.isNotBlank(hotelMetaData.getPropertyDetails().getStayType()) && 
            StringUtils.startsWith(hotelMetaData.getPropertyDetails().getStayType().toUpperCase(), "ENTIRE")) {
            hotelResult.setEntireProperty(true);
        }

        if (hotelMetaData.getHostingInfo() != null && hotelMetaData.getHostingInfo().getHostInfo() != null) {
            hotelResult.setHostInfoV2(buildHostInfoV2(hotelMetaData.getHostingInfo().getHostInfo(), hotelMetaData.getRatingDataMap(),  isDisableHostChat));
        }

        hotelResult.setPopularType(hotelMetaData.getPropertyDetails().getPopularType());

        if (hotelMetaData.getHostingInfo() != null && hotelMetaData.getHostingInfo().getStaffInfo() != null) {
            hotelResult.setStaffInfo(convertStaffInfo(hotelMetaData.getHostingInfo().getStaffInfo()));
        }

        hotelResult.setFlexibleCheckinInfo(mapFlexibleCheckinInfo(hotelMetaData.getCheckInOutInfo() != null ? hotelMetaData.getCheckInOutInfo().getFlexibleCheckinInfo() : null));

        if (isGroupBookingFunnel) {
            hotelResult.setHighlightedAmenities(utility.getHighlightedAmenities(hotelMetaData.getAmenitiesInfo().getHighlightedAmenities()));
        } else {
            hotelResult.setHighlightedAmenities(utility.getHighlightedAmenities(hotelMetaData.getAmenitiesInfo().getHighlightedAmenities()));
            hotelResult.setHighlightedAmenitiesV2(utility.getHighlightedAmenitiesV2(hotelMetaData.getAmenitiesInfo().getHighlightedAmenities()));
        }
        
        // Map highlighted amenity tag if available
        hotelResult.setHighlightedAmenitiesTag(hotelMetaData.getAmenitiesInfo().getHighlightedAmenityTag());

        Set<SpaceData> sharedSpacesList = new HashSet<>();
        Set<SpaceData> privateSpacesList = new HashSet<>();
        AtomicReference<SharedInfo> sharedInfo = new AtomicReference<>();
        if (hotelMetaData.getAltAccoRoomInfo() != null) {
            hotelMetaData.getAltAccoRoomInfo().forEach((key, roomInfo) -> {
                if (roomInfo != null && CollectionUtils.isNotEmpty(roomInfo.getSpaces())) {
                    roomInfo.getSpaces().forEach(spaceData -> {
                        if (spaceData.getType() == com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.SpaceData.Type.SHARED) {
                            sharedInfo.set(utility.buildSharedInfo(spaceData.getDisplayItem()));
                            utility.getSpaceDataV2(spaceData, false, sharedSpacesList);
                        } else {
                            utility.getSpaceDataV2(spaceData, true, privateSpacesList);
                        }
                    });
                }
            });
        }

        if (utility.isExperimentOn(expDataMap, EXP_PL_GI) && MapUtils.isNotEmpty(hotelMetaData.getAltAccoRoomInfo())) {
            hotelResult.setGiSharedSpacesV2(sharedSpacesList);
            //GIHTL-15674 Remove property layout section where only 1 room type available as private space and no shared space available.
            //But for entire property case,it should be shown
            if (CollectionUtils.isNotEmpty(privateSpacesList) || Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelMetaData.getPropertyDetails().getListingType())) {
                hotelResult.setGiPrivateSpacesV2(privateSpacesList);
            }
            if (Constants.LISTING_TYPE_ENTIRE.equalsIgnoreCase(hotelMetaData.getPropertyDetails().getListingType())) {
                hotelResult.setPropertyLayoutTitleText(MessageFormat.format(polyglotService.getTranslatedData(ENTIRE_PROPERTY_LAYOUT_TEXT), hotelMetaData.getPropertyDetails().getPropertyType()));
            } else {
                hotelResult.setPropertyLayoutTitleText(polyglotService.getTranslatedData(ROOM_BY_ROOM_PROPERTY_LAYOUT_TEXT));
            }
        }

        hotelResult.setHouseRules(buildHouseRules(hotelMetaData.getRulesAndPolicies().getHouseRules(), hotelMetaData.getRulesAndPolicies().getMustReadRules()));
        hotelResult.setHouseRulesV2(buildHouseRulesV2(hotelResult.getHouseRules(), hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules()));
        
        if (isLiteResponse) {
            hotelResult.setHouseRules(null);
        }

        boolean isInternational = staticDetailRequestBody != null && utility.isInternationalProperty(hotelMetaData.getLocationInfo(), staticDetailRequestBody.getSearchCriteria());
        boolean supressHouseRules = isInternational && !hotelMetaData.getPropertyFlags().isAltAcco();
        hotelResult.setSupressHouseRules(supressHouseRules);
        
        boolean isLuxe = hotelMetaData.getPropertyDetails() != null && hotelMetaData.getPropertyDetails().getCategories() != null && hotelMetaData.getPropertyDetails().getCategories().contains(LUXURY_HOTELS);

        if(hotelMetaData.getPropertyFlags().isAltAcco() && CollectionUtils.isNotEmpty(hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules())) {
            hotelResult.setFoodDining(buildFoodDining(hotelMetaData.getRulesAndPolicies().getFoodAndDiningRules(), staticDetailRequestBody.getSearchCriteria(), staticDetailRequestBody.getDeviceDetails(), MapUtils.isNotEmpty(hotelMetaData.getRatingDataMap()) ? hotelMetaData.getRatingDataMap().getOrDefault(RatingCategory.FOOD, new RatingData()) : new RatingData(), hotelMetaData.getPropertyFlags().isAltAcco(), dhCall, true));
        } else {
            hotelResult.setFoodAndDining(mapFoodAndDiningRules(hotelMetaData.getRulesAndPolicies()));
        }

        if (FUNNEL_SOURCE_DAYUSE.equalsIgnoreCase(funnel))
            suppressFewHouseRules(hotelResult.getHouseRulesV2());

        hotelResult.setFaqData(buildFaqData(hotelMetaData.getFrequentlyAskedQuestions()));

        if (StringUtils.isNotBlank(hotelMetaData.getPropertyDetails().getStayType()) && StringUtils.startsWith(hotelMetaData.getPropertyDetails().getStayType().toUpperCase(), "ENTIRE")) {
            hotelResult.setEntireProperty(true);
        }

        hotelResult.setSharingUrl(hotelMetaData.getPropertyDetails().getSharingUrl());

        hotelResult.setLocationPersuasion(hotelMetaData.getLocationInfo().getLocationPersuasion());
        hotelResult.setIngoId(hotelMetaData.getPropertyDetails().getIngoHotelId());
        hotelResult.setContextType(hotelMetaData.getPropertyDetails().getContext());
        
        hotelResult.setContext(context);
        
        if (isLuxe) {
            hotelResult.setLuxeIcon(getLuxeIcon());
        }

        hotelResult.setMmtHotelText(hotelMetaData.getPropertyDetails().getPopularText());

        hotelResult.setCardTitleMap(buildCardTitleMap());
        if(hotelMetaData.getMediaContent() != null && StringUtils.isNotBlank(hotelMetaData.getMediaContent().getHeroImage())) {
            hotelResult.setHeroImage(hotelMetaData.getMediaContent().getHeroImage());
        }
        if(hotelMetaData.getMediaContent() != null && StringUtils.isNotBlank(hotelMetaData.getMediaContent().getHeroVideoUrl())) {
            hotelResult.setHeroVideoUrl(hotelMetaData.getMediaContent().getHeroVideoUrl());
        }
        
        if (hotelResult.getHostInfo() == null || !hotelResult.getHostInfo().isChatEnabled()) {
            hotelResult.setGroupBookingQueryEnabled(hotelMetaData.getPropertyFlags().isGroupBookingAllowed());
        }
        
        hotelResult.setWishListed(hotelMetaData.getPropertyFlags().isWishListed());
        hotelResult.setCalendarCriteria(buildCalendarCriteria(hotelMetaData.getCalendarCriteria()));
        
        if (hotelMetaData.getRulesAndPolicies() != null && CollectionUtils.isNotEmpty(hotelMetaData.getRulesAndPolicies().getGovtPolicies())) {
            hotelResult.setGovtPolicies(buildGovtPolies(hotelMetaData.getRulesAndPolicies().getGovtPolicies()));
        }
        
        if (StringUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategoryUsp()) && !isLiteResponse) {
            hotelResult.setCategoryUspDetailsText(polyglotService.getTranslatedData(BEACHFRONT_CATEGORY_USP_DETAILS_TEXT));
        }

        List<String> list = new ArrayList<>(hotelMetaData.getPropertyDetails().getCategories());
        hotelResult.setCategories(list);

        hotelResult.setGoStayAssured(isGoStayAssured(hotelMetaData));

        return hotelResult;
    }

    private boolean isGoStayAssured(HotelMetaData hotelMetaData) {
        return hotelMetaData != null && hotelMetaData.getPropertyDetails() != null &&
                CollectionUtils.isNotEmpty(hotelMetaData.getPropertyDetails().getCategories()) &&
                hotelMetaData.getPropertyDetails().getCategories().contains(Constants.GO_STAYS_V2_CATEGORY);
    }

    private HostInfoV2 buildHostInfoV2(com.gommt.hotels.orchestrator.detail.model.response.content.HostInfo hostInfo, Map<RatingCategory, RatingData> ratingDataMap, boolean isDisableHostChat) {
        if (hostInfo == null) {
            return null;
        }

        HostInfoV2 hostInfoV2 = new HostInfoV2();

        // Direct field mappings
        hostInfoV2.setName(hostInfo.getName());
        if(StringUtils.isNotEmpty(hostInfo.getAbout())) {
            hostInfoV2.setAbout(hostInfo.getAbout());
        }
        hostInfoV2.setHostType(hostInfo.getHostType());
        hostInfoV2.setTimeSinceHostingOnMmt(hostInfo.getTimeSinceHostingOnMmt());
        hostInfoV2.setHostImage(hostInfo.getHostImage());
        hostInfoV2.setResponseTime(hostInfo.getResponseTime());
        if(StringUtils.isNotBlank(hostInfo.getResponseRate())) {
            hostInfoV2.setResponseRate(hostInfo.getResponseRate());
        }
        hostInfoV2.setLanguage(hostInfo.getLanguage());
        hostInfoV2.setCaretakerAvailability(hostInfo.getCaretakerAvailability());
        hostInfoV2.setCaretakerResponsibilities(hostInfo.getCaretakerResponsibilities());
        hostInfoV2.setHostedPropertyText(hostInfo.getHostedPropertyText());
        hostInfoV2.setHostedPropertyIcon(hostInfo.getHostedPropertyIcon());
        hostInfoV2.setHostStayText(hostInfo.getHostStayText());
        hostInfoV2.setHostStayIcon(hostInfo.getHostStayIcon());

        // Convert RatingData to UGCRatingData if present
        if (MapUtils.isNotEmpty(ratingDataMap) && ratingDataMap.containsKey(RatingCategory.CARETAKER)) {
            hostInfoV2.setCareTakerRating(buildRatingData(ratingDataMap.get(RatingCategory.CARETAKER)));
        }

        return hostInfoV2;
    }

    private UGCRatingData buildRatingData(RatingData ratingData) {
        if (ratingData == null) {
            return null;
        }

        UGCRatingData ugcRatingData = new UGCRatingData();

        // Map title
        if (ratingData.getTitle() != null) {
            ugcRatingData.setTitle(ratingData.getTitle());
        }

        // Map subtitle
        if (ratingData.getSubTitle() != null) {
            ugcRatingData.setSubTitle(ratingData.getSubTitle());
        }

        // Map show icon flag
        ugcRatingData.setShowIcon(ratingData.isShowIcon());

        // Map summary if available
        if (ratingData.getSummary() != null) {
            RatingDetail summaryDetail = new RatingDetail();
            if (ratingData.getSummary().getText() != null) {
                summaryDetail.setText(ratingData.getSummary().getText());
            }
            // Set icon URL from rating data if available
            if (ratingData.getSummary().getIconUrl() != null) {
                summaryDetail.setIconUrl(ratingData.getSummary().getIconUrl());
            }
            ugcRatingData.setSummary(summaryDetail);
        }

        // Map highlights if available
        if (CollectionUtils.isNotEmpty(ratingData.getHighlights())) {
            List<RatingDetail> highlights = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem highlight : ratingData.getHighlights()) {
                RatingDetail highlightDetail = new RatingDetail();
                if (highlight.getText() != null) {
                    highlightDetail.setText(highlight.getText());
                }
                if (highlight.getIconUrl() != null) {
                    highlightDetail.setIconUrl(highlight.getIconUrl());
                }
                highlights.add(highlightDetail);
            }
            ugcRatingData.setHighlights(highlights);
        }

        return ugcRatingData;
    }

    private void suppressFewHouseRules(HouseRulesV2 houseRulesV2) {
        if (null == houseRulesV2 || CollectionUtils.isEmpty(houseRulesV2.getAllRules())) return;
        List<CommonRules> resultCommonRulesList = houseRulesV2.getAllRules().stream().filter(rule -> !supressedHouseRulesList.contains(rule.getCategoryId()) && (null == rule.getId() || !rule.getId().equalsIgnoreCase(EXTRA_BED_POLICY_TO_BE_REMOVED))).collect(Collectors.toList());
        houseRulesV2.setAllRules(resultCommonRulesList);
    }

    private HouseRulesV2 buildHouseRulesV2(HouseRules houseRules, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> foodAndDiningRule) {
        if (houseRules == null) {
            return null;
        }
        HouseRulesV2 houseRulesV2 = new HouseRulesV2();
        houseRulesV2.setContextRules(houseRules.getContextRules());

        List<CommonRules> allRules = new ArrayList<>();
        if (houseRules.getMustReadRules() != null) {
            allRules.add(convertMustReadRule(houseRules.getMustReadRules()));
        }

        if (CollectionUtils.isNotEmpty(houseRules.getCommonRules())) {
            for (CommonRules commonRules : houseRules.getCommonRules()) {
                if (Constants.GUEST_PROFILE.equalsIgnoreCase(commonRules.getId()) || Constants.SAFETY_AND_HYGIENE.equalsIgnoreCase(commonRules.getId())) {
                    commonRules.setShowInDetailHome(true);
                }
                allRules.add(commonRules);
            }
        }

        List<Rule> rules = new ArrayList<>();
        List<ChildExtraBedPolicy> extraBedPolicy = houseRules.getExtraBedPolicyList();
        if (CollectionUtils.isNotEmpty(extraBedPolicy)) {
            for (ChildExtraBedPolicy childExtraBedPolicy : extraBedPolicy) {
                rules.add(new Rule(childExtraBedPolicy.getPolicyInfo()));
                if (CollectionUtils.isNotEmpty(childExtraBedPolicy.getPolicyRules())) {
                    for (PolicyRules policyRules : childExtraBedPolicy.getPolicyRules()) {
                        if (CollectionUtils.isNotEmpty(policyRules.getExtraBedTerms())) {
                            for (ExtraBedRules extraBedRules : policyRules.getExtraBedTerms()) {
                                if (extraBedRules.getValue() != null) {
                                    rules.add(new Rule(extraBedRules.getValue()));
                                }
                            }
                        }
                    }
                }
            }
            CommonRules commonRules = new CommonRules();
            commonRules.setCategory(polyglotService.getTranslatedData(EXTRA_BED_POLICY));
            commonRules.setId(EXTRA_BED_POLICY.toLowerCase());
            commonRules.setRules(rules);
            allRules.add(commonRules);
        }

        houseRulesV2.setAllRules(allRules);
        return houseRulesV2;
    }

//    private ContextRules buildContextRules(Set<String> categories, String countryCode) {
//        if (CollectionUtils.isEmpty(categories) ||
//                !categories.contains(COUPLE_FRIENDLY) ||
//                !DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
//            return null;
//        }
//
//        ContextRules contextRules = new ContextRules();
//        contextRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_TITLE));
//        contextRules.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_RULE_TITLE));
//        contextRules.setDesc(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_RULE_DESC));
//        contextRules.setTag(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_TITLE));
//        contextRules.setRuleIcon("https://promos.makemytrip.com/Hotels_product/Details/Couplefriendly2x.png");
//        return contextRules;
//    }

    private CommonRules convertMustReadRule(CommonRules mustReadRules){
        if (CollectionUtils.isNotEmpty(mustReadRules.getRulesList())) {
            List<Rule> rules = mustReadRules.getRulesList().stream().map(Rule::new).collect(Collectors.toList());
            mustReadRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.RESTRICTIONS));
            mustReadRules.setId(RESTRICTIONS.toLowerCase());
            mustReadRules.setShowInDetailHome(true);
            mustReadRules.setExpandRules(true);
            mustReadRules.setRules(rules);
            mustReadRules.setRulesList(null);
        }
        return mustReadRules;
    }

    private HouseRules buildHouseRules(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.HouseRules houseRules, List<String> mustReadRules) {
        HouseRules houseRulesCG = null;
        if (houseRules != null ) {
            houseRulesCG = new HouseRules();
            // Use first child extra bed policy if available
            if (CollectionUtils.isNotEmpty(houseRules.getChildExtraBedPolicies())) {
                houseRulesCG.setChildExtraBedPolicy(buildChildExtraBedPolicy(houseRules.getChildExtraBedPolicies().get(0)));
                houseRulesCG.setExtraBedPolicyList(buildExtraBedPolicyList(houseRules.getChildExtraBedPolicies()));
            }
            houseRulesCG.setCommonRules(buildCommonRules(houseRules.getCommonRules()));
            // Extra bed policy list and other info mapping - implementation pending
            //houseRulesCG.setExtraBedPolicyList(buildExtraBedPolicyList(houseRules.getExtraBedPolicyList()));
            // houseRulesCG.setOtherInfo(buildCommonRules(houseRules.getOtherInfo()));
            houseRulesCG.setMustReadRules(buildMustReadRules(mustReadRules));
//            if (houseRules.getContextRules() != null) {
//                ContextRules contextRulesCg = new ContextRules();
//                contextRulesCg.setCategory(houseRules.getContextRules().getCategory());
//                contextRulesCg.setRuleIcon(houseRules.getContextRules().getRuleIcon());
//                contextRulesCg.setDesc(houseRules.getContextRules().getDesc());
//                contextRulesCg.setTag(houseRules.getContextRules().getTag());
//                contextRulesCg.setTitle(houseRules.getContextRules().getTitle());
//                houseRulesCG.setContextRules(contextRulesCg);
//            }
        }
        return houseRulesCG;
    }

//    private ContextRules buildContextRules(Set<String> categories, String countryCode) {
//        if (CollectionUtils.isEmpty(categories) ||
//                !categories.contains(COUPLE_FRIENDLY) ||
//                !DOM_COUNTRY.equalsIgnoreCase(countryCode)) {
//            return null;
//        }
//
//        ContextRules contextRules = new ContextRules();
//        contextRules.setCategory(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_TITLE));
//        contextRules.setTitle(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_RULE_TITLE));
//        contextRules.setDesc(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_RULE_DESC));
//        contextRules.setTag(polyglotService.getTranslatedData(ConstantsTranslation.COUPLE_FRIENDLY_TITLE));
//        contextRules.setRuleIcon("https://promos.makemytrip.com/Hotels_product/Details/Couplefriendly2x.png");
//        return contextRules;
//    }


    private FaqData buildFaqData(List<FrequentlyAskedQuestion> frequentlyAskedQuestions) {
        FaqData faqData = null;
        if (frequentlyAskedQuestions != null && !frequentlyAskedQuestions.isEmpty()) {
            faqData = new FaqData();
            List<Faqs> faqsList = new ArrayList<>();
            
            for (FrequentlyAskedQuestion frequentlyAskedQuestion : frequentlyAskedQuestions) {
                Faqs faqs = new Faqs();
                if (frequentlyAskedQuestion.getQuestion() != null) {
                    faqs.setQuestion(frequentlyAskedQuestion.getQuestion());
                }
                if (frequentlyAskedQuestion.getAnswer() != null) {
                    faqs.setAnswer(frequentlyAskedQuestion.getAnswer());
                }
                faqsList.add(faqs);
            }
            
            faqData.setFaqs(faqsList);
            faqData.setTitle(FAQ_TITLE);
            faqData.setHint(FAQ_HINT);
            faqData.setItemCountForCard(Integer.valueOf(FAQ_ITEM_COUNT));
            faqData.setExtraItemText(FAQ_EXTRA_TEXT.replace("%d", "" + frequentlyAskedQuestions.size()));
        }
        return faqData;
    }

    private CommonRules buildMustReadRules(List<String> mustReadRules) {
        if(CollectionUtils.isEmpty(mustReadRules)) {
            return null;
        }
        CommonRules mustReadRulesCG = new CommonRules();
        mustReadRulesCG.setCategory("must read");
        mustReadRulesCG.setRulesList(mustReadRules);
        return mustReadRulesCG;
    }

    private List<ChildExtraBedPolicy> buildExtraBedPolicyList(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ChildExtraBedPolicy> extraBedPolicyList) {
        List<ChildExtraBedPolicy> listCG = null;
        if (CollectionUtils.isNotEmpty(extraBedPolicyList)){
            listCG = new ArrayList<>();
            for(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ChildExtraBedPolicy policy : extraBedPolicyList){
                listCG.add(buildChildExtraBedPolicy(policy));
            }
        }
        return listCG;
    }

    private List<CommonRules> buildCommonRules(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> commonRules) {
        List<CommonRules> commonRulesCG = null;
        if (CollectionUtils.isNotEmpty(commonRules)) {
            commonRulesCG = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule : commonRules) {
                CommonRules commonRuleCG = new CommonRules();
                List<Rule> rules = buildRules(commonRule.getRules());
                if (CollectionUtils.isEmpty(rules)) {
                    continue;
                }
                commonRuleCG.setRules(rules);
                commonRuleCG.setCategory(commonRule.getCategory());
                commonRuleCG.setId(commonRule.getId());
                commonRuleCG.setHostCatHeading(commonRule.getHeading());
                commonRuleCG.setShowInHost(commonRule.isShowInHost());
                commonRuleCG.setShowInDetailHome(commonRule.isShowInL1Page());
                commonRuleCG.setExpandRules(commonRule.isExpandable());

                if (StringUtils.isNotEmpty(commonRule.getCategoryId())) {
                    commonRuleCG.setCategoryId(Integer.valueOf(commonRule.getCategoryId()));
                }
                if (commonRule.getGallery() != null) {
                    commonRuleCG.setImages(commonRule.getGallery().getUrls());
                    commonRuleCG.setShowInL2Page(commonRule.isShowInL2Page());
                }
                commonRulesCG.add(commonRuleCG);
            }
        }
        return commonRulesCG;
    }

    private List<Rule> buildRules(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule> rules) {
        List<Rule> rulesCG = null;
        if (CollectionUtils.isNotEmpty(rules)) {
            rulesCG = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule : rules) {
                Rule ruleCG = new Rule();
                ruleCG.setDisplayRank(rule.getDisplayRank());
                ruleCG.setSentiment(rule.getSentiment());
                ruleCG.setTemplateText(rule.getTemplateText());
                ruleCG.setText(rule.getText());
                ruleCG.setRequired(rule.isRequired());
                ruleCG.setValues(rule.getValues());
                rulesCG.add(ruleCG);
            }
        }
        return rulesCG;
    }

    private ChildExtraBedPolicy buildChildExtraBedPolicy(
            com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ChildExtraBedPolicy childExtraBedPolicy) {
        if (null == childExtraBedPolicy){
            return null;
        }
        ChildExtraBedPolicy childExtraBedPolicyCG = new ChildExtraBedPolicy();
        childExtraBedPolicyCG.setId(childExtraBedPolicy.getId());
        childExtraBedPolicyCG.setLabel(childExtraBedPolicy.getLabel());
        childExtraBedPolicyCG.setPaid(childExtraBedPolicy.getPaid());
        childExtraBedPolicyCG.setPolicyInfo(childExtraBedPolicy.getPolicyInfo());
        childExtraBedPolicyCG.setPolicyRules(buildPolicyRules(childExtraBedPolicy.getPolicyRules()));
        return childExtraBedPolicyCG;
    }

    private List<PolicyRules> buildPolicyRules(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.PolicyRules> policyRules) {
        List<PolicyRules> policyRulesCG = null;
        if (CollectionUtils.isNotEmpty(policyRules)){
            policyRulesCG = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.PolicyRules policyRule : policyRules){
                PolicyRules PolicyRulesCG = new PolicyRules();
                PolicyRulesCG.setAgeGroup(policyRule.getAgeGroup());
                PolicyRulesCG.setExtraBedTerms(buildExtraBedTerms(policyRule.getExtraBedTerms()));
                policyRulesCG.add(PolicyRulesCG);
            }
        }
        return policyRulesCG;
    }

    private Set<ExtraBedRules> buildExtraBedTerms(Set<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ExtraBedRules> extraBedTerms) {
        Set<ExtraBedRules> extraBedRulesCG = null;
        if (CollectionUtils.isNotEmpty(extraBedTerms)){
            extraBedRulesCG = new HashSet<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.ExtraBedRules extraBedTerm : extraBedTerms){
                ExtraBedRules extraBedRuleCG = new ExtraBedRules();
                extraBedRuleCG.setLabel(extraBedTerm.getLabel());
                extraBedRuleCG.setValue(extraBedTerm.getValue());
                extraBedRulesCG.add(extraBedRuleCG);
            }
        }
        return extraBedRulesCG;
    }

    private List<GovtPolicies> buildGovtPolies(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> commonRules) {
        List<GovtPolicies> govtPolicies = new ArrayList<>();
            commonRules.forEach(govtPolicyFromHES -> {
                if(govtPolicyFromHES != null) {
                    GovtPolicies govPolicy = new GovtPolicies();
                    govPolicy.setNidhiId(govtPolicyFromHES.getId());
                    if(CollectionUtils.isNotEmpty(govtPolicyFromHES.getRules())) {
                        govPolicy.setLogoUrl(govtPolicyFromHES.getRules().get(0).getIconUrl());
                    }
                    govPolicy.setTitle(govtPolicyFromHES.getTitle());
                    govPolicy.setSubTitle(govtPolicyFromHES.getSubTitle());
                    govtPolicies.add(govPolicy);
                }
            });
        return govtPolicies;

    }

    private CalendarCriteria buildCalendarCriteria(com.gommt.hotels.orchestrator.detail.model.response.content.CalendarCriteria calendarCriteriaHES) {
        if(calendarCriteriaHES == null)
            return null;
        CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
        calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getDisplayDuration());
        calendarCriteriaCG.setAvailable(calendarCriteriaHES.isEnabled());
        calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getEndDate());
        calendarCriteriaCG.setMlos(calendarCriteriaHES.getMinimumStayLength());
        return calendarCriteriaCG;
    }

    private LocationDetail buildLocationDetail(String cityCode, String cityName, String cityCtyCode, String country) {
        LocationDetail locationDetail = new LocationDetail();
        locationDetail.setId(cityCode);
        locationDetail.setName(cityName);
        locationDetail.setType("city");
        locationDetail.setCountryId(cityCtyCode);
        locationDetail.setCountryName(country);
        return locationDetail;
    }


    private PropertyChainCG getPropertyChain(PropertyChain propertyChain) {
        PropertyChainCG propertyChainCG = new PropertyChainCG();
        if (propertyChain.getDetails() != null) propertyChainCG.setDetails(getPropertyDetails(propertyChain.getDetails()));
        propertyChainCG.setSummary(propertyChain.getSummary());
        propertyChainCG.setLogo(propertyChain.getLogo());
        return propertyChainCG;
    }

    private PropertyChainDetailCG getPropertyDetails(PropertyChainDetails details) {
        PropertyChainDetailCG propertyChainDetailCG = new PropertyChainDetailCG();
        propertyChainDetailCG.setTitle(details.getTitle());
        propertyChainDetailCG.setDesc(details.getDesc());
        return propertyChainDetailCG;
    }


    private PropertyHighlightCG getPropertyHighLights(com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlights propertyHighlights) {
        PropertyHighlightCG propertyHighlightCG = new PropertyHighlightCG();
        if (propertyHighlights.getDetails() != null) propertyHighlightCG.setDetails(getDetails(propertyHighlights.getDetails()));
        propertyHighlightCG.setTitle(propertyHighlights.getTitle());
        propertyHighlightCG.setIcon(propertyHighlights.getIcon());
        return propertyHighlightCG;
    }

    private List<PropertyHighlightDetailCG> getDetails(List<com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlightDetails> details) {
        List<PropertyHighlightDetailCG> highlightDetailCGList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(details)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlightDetails detail: details) {
                PropertyHighlightDetailCG propertyHighlightDetailCG = new PropertyHighlightDetailCG();
                propertyHighlightDetailCG.setDesc(detail.getDesc());
                propertyHighlightDetailCG.setTitle(detail.getTitle());
                propertyHighlightDetailCG.setIcon(detail.getIcon());
                highlightDetailCGList.add(propertyHighlightDetailCG);
            }
        }
        return highlightDetailCGList;
    }

    private FlexibleCheckinInfo mapFlexibleCheckinInfo(
            com.gommt.hotels.orchestrator.detail.model.response.content.flexiblecheckin.FlexibleCheckinInfo flexibleCheckinInfo) {
        if (flexibleCheckinInfo == null) {
            return null;
        }
        
        FlexibleCheckinInfo mappedInfo =
                new FlexibleCheckinInfo();
        
        // Map title
        if (flexibleCheckinInfo.getTitle() != null) {
            mappedInfo.setTitle(flexibleCheckinInfo.getTitle());
        }
        
        // Map subtitle
        if (flexibleCheckinInfo.getSubTitle() != null) {
            mappedInfo.setSubTitle(flexibleCheckinInfo.getSubTitle());
        }
        
        // Map default slot message
        if (flexibleCheckinInfo.getDefaultSlotMsg() != null) {
            mappedInfo.setDefaultSlotMsg(flexibleCheckinInfo.getDefaultSlotMsg());
        }
        
        // Map time slots
        if (flexibleCheckinInfo.getTimeSlots() != null && !flexibleCheckinInfo.getTimeSlots().isEmpty()) {
            List<TimeSlot> mappedTimeSlots = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.flexiblecheckin.TimeSlot timeSlot : flexibleCheckinInfo.getTimeSlots()) {
                TimeSlot mappedSlot = new TimeSlot();
                if (timeSlot.getId() != null) {
                    mappedSlot.setId(timeSlot.getId());
                }
                if (timeSlot.getValue() != null) {
                    mappedSlot.setValue(timeSlot.getValue());
                }
                mappedTimeSlots.add(mappedSlot);
            }
            mappedInfo.setTimeSlots(mappedTimeSlots);
        }
        
        // Map tag URL
        if (flexibleCheckinInfo.getTagUrl() != null) {
            mappedInfo.setTagUrl(flexibleCheckinInfo.getTagUrl());
        }
        
        // Map subtitle default
        if (flexibleCheckinInfo.getSubTitleDefault() != null) {
            mappedInfo.setSubTitleDefault(flexibleCheckinInfo.getSubTitleDefault());
        }
        
        // Map subtitle slot selected
        if (flexibleCheckinInfo.getSubTitleSlotSelected() != null) {
            mappedInfo.setSubTitleSlotSelected(flexibleCheckinInfo.getSubTitleSlotSelected());
        }
        
        // Map tag info
        if (flexibleCheckinInfo.getTagInfo() != null) {
            TagInfo mappedTagInfo = new TagInfo();
            
            if (flexibleCheckinInfo.getTagInfo().getText() != null) {
                mappedTagInfo.setText(flexibleCheckinInfo.getTagInfo().getText());
            }
            
            if (flexibleCheckinInfo.getTagInfo().getTextColor() != null) {
                mappedTagInfo.setTextColor(flexibleCheckinInfo.getTagInfo().getTextColor());
            }
            
            // Map BgGradient if it exists
            if (flexibleCheckinInfo.getTagInfo().getBgGradient() != null) {
                com.mmt.hotels.model.persuasion.response.BgGradient mappedBgGradient = new com.mmt.hotels.model.persuasion.response.BgGradient();
                // Map BgGradient fields - assuming similar structure
                // You may need to adjust this based on the actual BgGradient structure
                mappedTagInfo.setBgGradient(mappedBgGradient);
            }
            
            mappedInfo.setTagInfo(mappedTagInfo);
        }
        
        // Map bottom sheet info
        if (flexibleCheckinInfo.getBottomSheetInfo() != null) {
            FlexiCheckinBottomSheet mappedBottomSheet =
                    new FlexiCheckinBottomSheet();
            
            if (flexibleCheckinInfo.getBottomSheetInfo().getTitle() != null) {
                mappedBottomSheet.setTitle(flexibleCheckinInfo.getBottomSheetInfo().getTitle());
            }
            
            if (flexibleCheckinInfo.getBottomSheetInfo().getDescription() != null) {
                mappedBottomSheet.setDescription(flexibleCheckinInfo.getBottomSheetInfo().getDescription());
            }
            
            // Map time slots in bottom sheet
            if (flexibleCheckinInfo.getBottomSheetInfo().getTimeSlots() != null && 
                    !flexibleCheckinInfo.getBottomSheetInfo().getTimeSlots().isEmpty()) {
                List<TimeSlot> mappedBottomSheetTimeSlots = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.content.flexiblecheckin.TimeSlot timeSlot : 
                        flexibleCheckinInfo.getBottomSheetInfo().getTimeSlots()) {
                    TimeSlot mappedSlot = new TimeSlot();
                    if (timeSlot.getId() != null) {
                        mappedSlot.setId(timeSlot.getId());
                    }
                    if (timeSlot.getValue() != null) {
                        mappedSlot.setValue(timeSlot.getValue());
                    }
                    mappedBottomSheetTimeSlots.add(mappedSlot);
                }
                mappedBottomSheet.setTimeSlots(mappedBottomSheetTimeSlots);
            }
            
            mappedInfo.setBottomSheetInfo(mappedBottomSheet);
        }
        
        return mappedInfo;
    }

    protected UGCRatingData mapRatingDataToUGCRatingData(RatingData ratingData) {
        if (ratingData == null) {
            return null;
        }

        UGCRatingData ugcRatingData = new UGCRatingData();

        // Map title
        if (ratingData.getTitle() != null) {
            ugcRatingData.setTitle(ratingData.getTitle());
        }

        // Map subtitle
        if (ratingData.getSubTitle() != null) {
            ugcRatingData.setSubTitle(ratingData.getSubTitle());
        }

        // Map show icon flag
        ugcRatingData.setShowIcon(ratingData.isShowIcon());

        // Map summary if available
        if (ratingData.getSummary() != null) {
            RatingDetail summaryDetail = new RatingDetail();
            if (ratingData.getSummary().getText() != null) {
                summaryDetail.setText(ratingData.getSummary().getText());
            }
            // Set icon URL from rating data if available
            if (ratingData.getIconUrl() != null) {
                summaryDetail.setIconUrl(ratingData.getIconUrl());
            }
            ugcRatingData.setSummary(summaryDetail);
        }

        // Map highlights if available
        if (ratingData.getHighlights() != null && !ratingData.getHighlights().isEmpty()) {
            List<RatingDetail> highlights = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem highlight : ratingData.getHighlights()) {
                RatingDetail highlightDetail = new RatingDetail();
                if (highlight.getText() != null) {
                    highlightDetail.setText(highlight.getText());
                }
                if (highlight.getIconUrl() != null) {
                    highlightDetail.setIconUrl(ratingData.getIconUrl());
                }
                highlights.add(highlightDetail);
            }
            ugcRatingData.setHighlights(highlights);
        }

        // Set merge ID to null as required for food dining
        //TODO :: ask Lepsy
        ugcRatingData.setMergeId(null);

        return ugcRatingData;
    }

    /**
     * Maps List<CommonRules> from orchestrator to FoodDining object for GI client gateway
     * @return FoodDining object for GI response
     */
    protected FoodDining mapFoodAndDiningRules(RulesAndPolicies rulesAndPolicies) {
        FoodDining foodDining = new FoodDining();
        if(rulesAndPolicies == null || CollectionUtils.isEmpty(rulesAndPolicies.getFoodAndDiningRules())) {
            return foodDining;
        }

        FoodRules propertyRules = new FoodRules();
        List<Info> allowedRules = new ArrayList<>();
        List<Info> notAllowedRules = new ArrayList<>();
        List<RestaurantDetails> restaurants = new ArrayList<>();
        
        boolean hasRestaurant = false;
        boolean hasRoomDining = false;

        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules rule : rulesAndPolicies.getFoodAndDiningRules()) {
            if(rule.getCategory().equalsIgnoreCase("RoomService")) {
                foodDining.setRoomServiceText("Food will be served in the room");
                RestaurantDetails restaurantDetails = new RestaurantDetails();
                FoodPointers foodPointers = new FoodPointers();
                foodPointers.setFoodType(new Info());
                foodPointers.setTimings(new Timings());
                foodPointers.setAvgMealCost(new Info());
                foodPointers.setCuisines(new Info());
                restaurantDetails.setDetails(foodPointers);
                foodDining.setRestaurants(Arrays.asList(restaurantDetails));
                hasRoomDining=true;
                continue;
            }
            if (rule.getCategory() == null) {
                continue;
            }
            String category = rule.getCategory().toUpperCase();
            
            switch (category) {
                case Constants.FOOD_DINING_ALLOWED:
                    if (CollectionUtils.isNotEmpty(rule.getRules())) {
                        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule ruleDetail : rule.getRules()) {
                            Info allowedInfo = new Info();
                            allowedInfo.setText(ruleDetail.getText());
                            allowedInfo.setIcon(ruleDetail.getIconUrl());
                            allowedRules.add(allowedInfo);
                        }
                    }
                    break;
                    
                case Constants.FOOD_DINING_NOT_ALLOWED:
                    if (CollectionUtils.isNotEmpty(rule.getRules())) {
                        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule ruleDetail : rule.getRules()) {
                            Info notAllowedInfo = new Info();
                            notAllowedInfo.setText(ruleDetail.getText());
                            notAllowedInfo.setIcon(ruleDetail.getIconUrl());
                            notAllowedRules.add(notAllowedInfo);
                        }
                    }
                    break;
                    
                case Constants.FOOD_ROOM_DINING_AT_PROPERTY:
                    hasRoomDining = true;
                    // Map room dining as a restaurant type
                    RestaurantDetails roomDiningDetails = mapCommonRuleToRestaurantDetails(rule);
                    if (roomDiningDetails != null) {
                        restaurants.add(roomDiningDetails);
                    }
                    break;
                    
                case Constants.FOOD_RESTAURANTS_AT_PROPERTY:
                    hasRestaurant = true;
                    // Map restaurant details from the rule
                    RestaurantDetails restaurantDetails = mapCommonRuleToRestaurantDetails(rule);
                    if (restaurantDetails != null) {
                        restaurants.add(restaurantDetails);
                    }
                    break;
                    
                default:
                    // Handle other categories if needed
                    break;
            }
        }

        // Set property rules only if we have rules to set
        if (CollectionUtils.isNotEmpty(allowedRules) || CollectionUtils.isNotEmpty(notAllowedRules)) {
            if (CollectionUtils.isNotEmpty(allowedRules)) {
                propertyRules.setAllowed(allowedRules);
            }
            if (CollectionUtils.isNotEmpty(notAllowedRules)) {
                propertyRules.setNotAllowed(notAllowedRules);
            }
            foodDining.setPropertyRules(propertyRules);
        }
        
        // Set restaurant and room dining flags
        foodDining.setHasRestaurant(hasRestaurant);
        foodDining.setHasRoomDining(hasRoomDining);
        
        // Set restaurants list if we have restaurant details
        if (CollectionUtils.isNotEmpty(restaurants)) {
            foodDining.setRestaurants(restaurants);
        }

        return foodDining;
    }

    /**
     * Maps CommonRules to RestaurantDetails for restaurant and room dining categories
     * @param commonRule The CommonRule containing restaurant/room dining information
     * @return RestaurantDetails object or null if no valid data
     */
    private RestaurantDetails mapCommonRuleToRestaurantDetails(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule) {
        if (commonRule == null) {
            return null;
        }

        RestaurantDetails restaurantDetails = new RestaurantDetails();
        
        // Map name from available fields (priority: heading -> title -> category)
        if (StringUtils.isNotEmpty(commonRule.getHeading())) {
            restaurantDetails.setName(commonRule.getHeading());
        } else if (StringUtils.isNotEmpty(commonRule.getTitle())) {
            restaurantDetails.setName(commonRule.getTitle());
        } else if (StringUtils.isNotEmpty(commonRule.getCategory())) {
            restaurantDetails.setName(commonRule.getCategory());
        }
        
        // Set type from defaultType
        //restaurantDetails.setType(commonRule);
        
        // Set cuisines from summaryText
        if (StringUtils.isNotEmpty(commonRule.getSummaryText())) {
            restaurantDetails.setCuisines(commonRule.getSummaryText());
        }
        
        // Map details (FoodPointers) from available data
        if(CollectionUtils.isNotEmpty(commonRule.getRules())) {
            FoodPointers details = mapToFoodPointers(commonRule.getRules(), restaurantDetails);
            if (details != null) {
                restaurantDetails.setDetails(details);
            }
            extractUspDishes(commonRule).ifPresent(info ->
                    restaurantDetails.setUspDishes(Collections.singletonList(info))
            );
        }

        
        // Map rules to uspDishes as Info objects (only displayable rules)
        if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
            List<Info> uspDishes = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule : commonRule.getRules()) {
                if (rule.isDisplay() && StringUtils.isNotEmpty(rule.getText())) {
                    Info dishInfo = new Info();
                    dishInfo.setText(rule.getText());
                    dishInfo.setIcon(rule.getIconUrl());
                    uspDishes.add(dishInfo);
                }
            }
            if (CollectionUtils.isNotEmpty(uspDishes)) {
                restaurantDetails.setUspDishes(uspDishes);
            }
        }


        // Map gallery if available
        if(CollectionUtils.isNotEmpty(commonRule.getGalleries())) {
            List<Gallery> galleryList = new ArrayList<>();
            for(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Gallery galleryOrch : commonRule.getGalleries()) {
//            if (commonRule.getGallery() != null) {
                Gallery gallery = new Gallery();
                if (galleryOrch.getTag() != null) {
                    //gallery.setMmtTag(commonRule.getGallery().getTag());
                    gallery.setGiTag(galleryOrch.getTag());
                }
                if (galleryOrch.getTagType() != null) {
                    gallery.setType(galleryOrch.getTagType());
                }
                gallery.setShowTagName(galleryOrch.isShowTag());

                if (CollectionUtils.isNotEmpty(galleryOrch.getUrls()) && galleryOrch.getUrls().size() >= 3) {
                    gallery.setBig(galleryOrch.getUrls().get(0));
                    gallery.setMedium(galleryOrch.getUrls().get(1));
                    gallery.setSmall(galleryOrch.getUrls().get(2));
                }
                galleryList.add(gallery);
            }
            restaurantDetails.setGallery(galleryList);
        }
        
        // Return the restaurant details if we have meaningful data
        if (StringUtils.isNotEmpty(restaurantDetails.getName()) || 
            StringUtils.isNotEmpty(restaurantDetails.getType()) ||
            StringUtils.isNotEmpty(restaurantDetails.getCuisines()) ||
            CollectionUtils.isNotEmpty(restaurantDetails.getUspDishes()) ||
            CollectionUtils.isNotEmpty(restaurantDetails.getGallery())) {
            return restaurantDetails;
        }
        
        return null;
    }

    private Optional<Info> extractUspDishes(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule) {
        if (commonRule == null || commonRule.getRules() == null) {
            return Optional.empty();
        }

        return commonRule.getRules().stream()
                .filter(rule -> "USP_DISHES".equalsIgnoreCase(rule.getTemplateId()))
                .findFirst()
                .map(rule -> {
                    Info info = new Info();
                    info.setText(rule.getText());
                    info.setIcon(rule.getIconUrl());
                    return info;
                });
    }


    /**
     * Maps CommonRule data to FoodPointers
     //* @param commonRule The CommonRule containing food information
     * @return FoodPointers object or null if no relevant data
     */
    private FoodPointers mapToFoodPointers(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule> rules, RestaurantDetails restaurantDetails) {
        FoodPointers foodPointers = new FoodPointers();

        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule : rules) {
            if (StringUtils.isEmpty(rule.getTemplateId())) {
                continue;
            }
            switch (rule.getTemplateId()) {
                case "FOOD_TYPE":
                    Info info = new Info();
                    info.setIcon(rule.getIconUrl());
                    info.setText(rule.getText());
                    foodPointers.setFoodType(info);
                    break;
                case "CUISINES":
                    Info cuisines = new Info();
                    cuisines.setIcon(rule.getIconUrl());
                    cuisines.setText(rule.getText());
                    foodPointers.setCuisines(cuisines);
                    break;
                case "AVERAGE_MEAL_COST":
                    Info mealCost = new Info();
                    mealCost.setIcon(rule.getIconUrl());
                    mealCost.setText(rule.getText());
                    foodPointers.setAvgMealCost(mealCost);
                    break;
                case "RESTAURANT_TIMINGS":
                    Timings timings = new Timings();
                    timings.setText(rule.getText());
                    timings.setIcon(rule.getIconUrl());
                    List<Meal> details = new ArrayList<>();
                    if (rule.getRuleTableInfo() != null && CollectionUtils.isNotEmpty(rule.getRuleTableInfo().getInfoList())) {
                        for (RuleInfo ruleInfo : rule.getRuleTableInfo().getInfoList()) {
                            Meal meal = new Meal();
                            if(CollectionUtils.isNotEmpty(ruleInfo.getValue())) {
                                String[] timingsKey = extractTimeRange(ruleInfo.getValue().get(0));
                                meal.setStart(timingsKey[0]);
                                meal.setMealType(ruleInfo.getKey());
                                meal.setEnd(timingsKey[1]);
                                details.add(meal);
                            }
                        }
                    }
                    timings.setDetails(details);
                    foodPointers.setTimings(timings);
                    break;
                case "RESTAURANT_TYPE":
                    restaurantDetails.setType(rule.getText());
                    break;
            }
        }
        restaurantDetails.setDetails(foodPointers);
        return foodPointers;
    }

    /**
     * Extracts time range from timing text
     * @param timingText The text containing time range
     * @return Array with start and end times, or empty array if no range found
     */
    private String[] extractTimeRange(String timingText) {
        if (StringUtils.isEmpty(timingText)) {
            return new String[0];
        }

        // Handle various separators like " - ", " to ", ":", etc.
        String[] separators = {" - ", " to ", "-", " till ", " until "};

        for (String separator : separators) {
            if (timingText.contains(separator)) {
                String[] parts = timingText.split(separator, 2);
                if (parts.length == 2) {
                    return new String[]{parts[0].trim(), parts[1].trim()};
                }
            }
        }

        return new String[0];
    }

    /**
     * Maps PremiumUsp from orchestrator PropertyHighlights to client gateway PremiumUsp
     * 
     * @param orchestratorPremiumUsp The PropertyHighlights from orchestrator PropertyDetails
     * @return Mapped PremiumUsp for client gateway
     */
    public com.mmt.hotels.model.response.staticdata.PremiumUsp mapPremiumUsp(
            com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlights orchestratorPremiumUsp) {
        if (orchestratorPremiumUsp == null) {
            return null;
        }
        
        com.mmt.hotels.model.response.staticdata.PremiumUsp premiumUsp =
            new com.mmt.hotels.model.response.staticdata.PremiumUsp();
        
        // Map title to summary
        premiumUsp.setSummary(orchestratorPremiumUsp.getTitle());
        
        // Map details to highlights
        if (CollectionUtils.isNotEmpty(orchestratorPremiumUsp.getDetails())) {
            List<com.mmt.hotels.model.response.staticdata.PremiumUspHighlight> highlights = new ArrayList<>();
            
            for (com.gommt.hotels.orchestrator.detail.model.response.content.PropertyHighlightDetails detail : orchestratorPremiumUsp.getDetails()) {
                if (detail != null) {
                    com.mmt.hotels.model.response.staticdata.PremiumUspHighlight highlight = 
                        new com.mmt.hotels.model.response.staticdata.PremiumUspHighlight();
                    
                    // Map fields from PropertyHighlightDetails to PremiumUspHighlight
                    highlight.setKey(detail.getTitle()); // Use title as key
                    highlight.setIconUrl(detail.getIcon());
                    highlight.setHeading(detail.getTitle());
                    highlight.setDescription(detail.getDesc());
                    
                    highlights.add(highlight);
                }
            }
            
            premiumUsp.setHighlights(highlights);
        }
        
        return premiumUsp;
    }


    public HouseRulesV2 buildFoodDining(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> foodDiningRule, StaticDetailCriteria staticDetailRequest, DeviceDetails deviceDetails, RatingData foodRatingData, boolean isAltAcco, boolean isDhCall, boolean foodAndDiningV2) {
        HouseRulesV2 foodDining = new HouseRulesV2();
        foodDining.setTitle(polyglotService.getTranslatedData(FOOD_AND_DINING));
        foodDining.setTag(true);
        if(foodRatingData != null){
            UGCRatingData ugcRatingData = mapRatingDataToUGCRatingData(foodRatingData);
            foodDining.setRatingData(ugcRatingData);
            if (foodDining.getRatingData() != null) {
                foodDining.getRatingData().setMergeId(null);
            }
        }

        List<CommonRules> allRulesList = new ArrayList<>();
        List<String> summaryList = new ArrayList<>();
        List<SummaryItem> summaryListV2 = new ArrayList<>();
        List<Restaurant> restaurants = new ArrayList<>();
        Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToRuleMap = buildSectionToRuleMap(foodDiningRule);

        if (MapUtils.isNotEmpty(sectionToRuleMap)) {
            for (Map.Entry<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToRuleEntry : sectionToRuleMap.entrySet()) {
                if(CollectionUtils.isNotEmpty(sectionToRuleEntry.getValue())){
                    for(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule : sectionToRuleEntry.getValue()) {
                        if (sectionToRuleEntry.getKey().equalsIgnoreCase(FoodAndDiningEnums.FoodMenu.getName())) {
                            continue;
                        }
                        CommonRules commonRules = new CommonRules();
                        commonRules.setCategory(commonRule.getCategory());
                        commonRules.setDescription(commonRule.getSummaryText());

                        if (commonRule.getCategory().equalsIgnoreCase(FoodAndDiningEnums.Meals.getName()) && sectionToRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())) {
                            commonRules.setRules(buildMealRuleList(sectionToRuleMap, deviceDetails));
                        } else if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
                            commonRules.setRules(buildRuleList(commonRule));
                        }

                        if (!(foodAndDiningV2 & ADDITIONAL_INFORMATION.equalsIgnoreCase(commonRule.getCategory()))) {
                            commonRules.setShowInDetailHome(true);
                        }
                        if (commonRule.getGallery() != null) {
                            commonRules.setImages(commonRule.getGallery().getUrls());
                        }
                        if(FoodAndDiningEnums.IndianFoodOptions.getName().equalsIgnoreCase(commonRule.getCategory()) ) {
                            commonRules.setId(commonRule.getId());
                            commonRules.setShowInDetailHome(commonRule.isShowInSummary());
                            commonRules.setShowInL2Page(commonRule.isShowInL2Page());
                        }
                        if(FoodAndDiningEnums.FoodAndDiningPropertyRules.getName().equalsIgnoreCase(commonRule.getCategory()) ) {
                            commonRules.setId(commonRule.getId());
                            commonRules.setShowInDetailHome(commonRule.isShowInSummary());
                            commonRules.setShowInL2Page(commonRule.isShowInL2Page());
                        }
                        if(FoodAndDiningEnums.Restaurant.getName().equalsIgnoreCase(commonRule.getCategory())){
                            commonRules.setId(commonRule.getId());
                            commonRules.setShowInDetailHome(commonRule.isShowInSummary());
                            if(Utility.isBookingDeviceDesktop(deviceDetails)){
                                commonRules.setShowInDetailHome(false);
                            }
                            if (isDhCall && commonRule.getGallery() != null && CollectionUtils.isNotEmpty(commonRule.getGallery().getUrls())) {
                                commonRules.setShowArrowInDetailHome(false);
                                restaurants.add(new Restaurant(commonRule.getHeading(), commonRule.getGallery().getUrls().get(0), commonRule.getId()));
                            }
                            if (StringUtils.isNotEmpty(commonRule.getUspText())) {
                                commonRules.setUspText(commonRule.getUspText());
                            }
                        }
                        allRulesList.add(commonRules);

                        if (!ADDITIONAL_INFORMATION.equalsIgnoreCase(commonRule.getCategory())
                                && !Utility.isBookingDeviceDesktop(deviceDetails) ){
                            if(foodAndDiningV2 && StringUtils.isNotEmpty(commonRule.getSummaryText())){
                                summaryList.add(commonRule.getSummaryText());
                            }
                            else if(StringUtils.isNotEmpty(commonRule.getHeading())){
                                summaryList.add(commonRule.getHeading());
                            }
                        }
                    }
                }
            }
        }
        foodDining.setRestaurants(CollectionUtils.isNotEmpty(restaurants) ? restaurants : null);

        foodDining.setAllRules(CollectionUtils.isNotEmpty(allRulesList) ? allRulesList : null);

        //if there is only one section then put ruleText from ruleList into summary list
        if (CollectionUtils.isNotEmpty(foodDiningRule) && foodDiningRule.size() == 1 && ADDITIONAL_INFORMATION.equalsIgnoreCase(foodDiningRule.get(0).getCategory())) {
            handleOneFoodAndDiningSection(foodDiningRule.get(0), summaryList, allRulesList);
        }

        // these are L1 overview pointers
        if (CollectionUtils.isNotEmpty(summaryList)) {
            // if childContext (child count > 0) is there in request, add node "Special meal for Kids is available on request"
            if (MapUtils.isNotEmpty(sectionToRuleMap) && sectionToRuleMap.containsKey(FoodAndDiningEnums.Cook.getName())) {
                Integer childCount = utility.getTotalChildrenFromRequest(staticDetailRequest.getRoomStayCandidates());
                if (childCount != null && childCount > 0) {
                    String mealForKids = polyglotService.getTranslatedData(ConstantsTranslation.MEAL_FOR_KIDS);
                    if (StringUtils.isNotEmpty(mealForKids) && !Constants.NULL_STRING.equalsIgnoreCase(mealForKids)) {
                        summaryList.add(mealForKids);
                    }
                }
            }
            foodDining.setSummary(summaryList);
            foodDining.setSummaryV2(summaryListV2);
        }
        return foodDining;
    }

    private Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> buildSectionToRuleMap(List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> foodDiningRule) {
        Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToRuleMap = new LinkedHashMap<>();

        if (CollectionUtils.isNotEmpty(foodDiningRule)) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule : foodDiningRule) {
                if (StringUtils.isNotEmpty(commonRule.getCategory())) {
                    if(!sectionToRuleMap.containsKey(commonRule.getCategory())) {
                        sectionToRuleMap.put(commonRule.getCategory(), new ArrayList<>());
                    }
                    List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules> rulesList = sectionToRuleMap.get(commonRule.getCategory());
                    rulesList.add(commonRule);
                    sectionToRuleMap.put(commonRule.getCategory(), rulesList);
                }
            }
        }
        return sectionToRuleMap;
    }

    private List<Rule> buildMealRuleList(Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules>> sectionToCommonRuleMap, DeviceDetails deviceDetails) {
        Rule foodMenuRule = null;
        if (sectionToCommonRuleMap.containsKey(FoodAndDiningEnums.FoodMenu.getName())
                && CollectionUtils.isNotEmpty(sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules())
                && null != sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules().get(0)) {
            com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule foodAndDiningMenuRule = sectionToCommonRuleMap.get(FoodAndDiningEnums.FoodMenu.getName()).get(0).getRules().get(0);
            foodMenuRule = new Rule();
            foodMenuRule.setText(foodAndDiningMenuRule.getText());
            foodMenuRule.setImages(foodAndDiningMenuRule.getImages());
            foodMenuRule.setImageCategory(foodAndDiningMenuRule.getImageCategory());
            //TODO :: Ask Lepsy
//            foodMenuRule.setInfoData(foodAndDiningMenuRule.getInfoData());
//            foodMenuRule.setIconUrl(utility.getUrlFromConfig(foodAndDiningMenuRule.getIconUrl()));
        }

        List<Rule> ruleList = buildRuleList(sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).get(0));
        if (deviceDetails != null && Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(deviceDetails.getBookingDevice())) {
            if (foodMenuRule != null && sectionToCommonRuleMap.get(FoodAndDiningEnums.Meals.getName()).get(0).getRules().size() > foodMenuPosition) {
                ruleList.add(foodMenuPosition, foodMenuRule);
            }
        } else {
            ruleList.add(foodMenuRule);
        }
        return ruleList;
    }

    private List<Rule> buildRuleList(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule) {
        List<Rule> ruleList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rules : commonRule.getRules()) {
            Rule rule = new Rule();
            rule.setText(rules.getText());
            rule.setIconUrl(utility.getUrlFromConfig(rules.getIconUrl()));
            rule.setInfoData(buildInfoData(rules.getRuleTableInfo()));
            ruleList.add(rule);
        }
        return ruleList;
    }

    private InfoData buildInfoData(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleTableInfo ruleTableInfo) {
        if (ruleTableInfo != null) {
            InfoData infoData = new InfoData();
            infoData.setTitle(ruleTableInfo.getKeyTitle());
            ArrayList<Datum> datumArrayList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.RuleInfo ruleInfo : ruleTableInfo.getInfoList()) {
                Datum datum = new Datum();
                datum.setKey(ruleInfo.getKey());
                if(CollectionUtils.isNotEmpty(ruleInfo.getValue())) {
                    datum.setValue(ruleInfo.getValue().get(0));
                }
                datumArrayList.add(datum);
            }
            if(CollectionUtils.isNotEmpty(datumArrayList)) {
                infoData.setData(datumArrayList);
                return infoData;
            }
        }
        return null;
    }

    private void handleOneFoodAndDiningSection(com.gommt.hotels.orchestrator.detail.model.response.content.houserules.CommonRules commonRule, List<String> summaryList, List<CommonRules> allRulesList) {
        summaryList.clear();
        if (CollectionUtils.isNotEmpty(commonRule.getRules())) {
            int count = 0;
            for (com.gommt.hotels.orchestrator.detail.model.response.content.houserules.Rule rule : commonRule.getRules()) {
                count++;
                summaryList.add(rule.getText());

                if (count == foodDiningMinCountConfig) {
                    break;
                }
            }
            allRulesList.clear();
        }
    }


}
