package com.mmt.hotels.clientgateway.transformer.request.desktop;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersRequest;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.transformer.request.SmartFiltersRequestTransformer;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SmartFiltersRequestTransformerDesktop extends SmartFiltersRequestTransformer {

    @Override
    protected void applyClientSpecificTransformations(
            SmartFiltersRequest smartFiltersRequest, 
            FilterCountRequest filterCountRequest, 
            List<String> filterTitles) {

        // Desktop-specific transformations can be added here
        // For now, just use the default implementation
    }
}
