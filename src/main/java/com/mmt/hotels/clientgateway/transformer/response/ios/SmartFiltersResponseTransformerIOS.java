package com.mmt.hotels.clientgateway.transformer.response.ios;

import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.transformer.response.SmartFiltersResponseTransformer;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SmartFiltersResponseTransformerIOS extends SmartFiltersResponseTransformer {
    
    @Override
    protected List<com.mmt.hotels.clientgateway.response.filter.Filter> applyClientSpecificTransformations(
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters,
            FilterCountRequest filterCountRequest) {
        
        // iOS-specific transformations can be added here
        // For now, just return the filters as-is
        return matchingFilters;
    }
}
