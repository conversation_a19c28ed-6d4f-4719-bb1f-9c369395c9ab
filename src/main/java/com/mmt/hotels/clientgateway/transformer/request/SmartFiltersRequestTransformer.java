package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersRequest;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.response.filter.Filter;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * Abstract class for transforming FilterCountRequest to SmartFiltersRequest for smart filter tags.
 * This follows the request transformer pattern used throughout the codebase.
 */
public abstract class SmartFiltersRequestTransformer {



    /**
     * Converts FilterCountRequest to SmartFiltersRequest for smart filter tags API.
     *
     * @param filterCountRequest The original filter count request
     * @param allFilters List of filter titles to match against
     * @return SmartFiltersRequest for the smart filter tags API
     */
    public SmartFiltersRequest convertSmartFiltersRequest(FilterCountRequest filterCountRequest, List<Filter> allFilters) {
        SmartFiltersRequest smartFiltersRequest = new SmartFiltersRequest();
        List<String> filterTitles = getTitlesFromFilters(allFilters);
        // Set basic properties
        smartFiltersRequest.setFilters(filterTitles);
        smartFiltersRequest.setQueryText(filterCountRequest.getSearchCriteria().getSearchQueryText());
        
        // Apply client-specific transformations
        applyClientSpecificTransformations(smartFiltersRequest, filterCountRequest, filterTitles);
        
        return smartFiltersRequest;
    }

    public List<String> getTitlesFromFilters(List<com.mmt.hotels.clientgateway.response.filter.Filter> filters) {
        List<String> titles = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(filters)) {
            for (com.mmt.hotels.clientgateway.response.filter.Filter filter : filters) {
                if (StringUtils.isNotEmpty(filter.getTitle())) {
                    String title = generateCustomTitle(filter);
                    titles.add(title);
                }
            }
        }
        return titles;
    }

    /**
     * Generates custom title for filters based on their filter group.
     * For filter groups defined in CUSTOM_TITLE_FILTER_GROUPS,
     * adds the filter group as a prefix followed by colon and then the filter title.
     *
     * @param filter The filter to generate title for
     * @return Custom title for the filter
     */
    private String generateCustomTitle(com.mmt.hotels.clientgateway.response.filter.Filter filter) {
        String filterGroup = filter.getFilterGroup();
        String filterTitle = filter.getTitle();
        
        // Check if the filter group requires custom title generation
        if (Constants.CUSTOM_TITLE_FILTER_GROUPS.contains(filterGroup)) {
            return filterGroup + ":" + filterTitle;
        }
        
        // For other filters, return the title as-is
        return filterTitle;
    }

    /**
     * Apply client-specific transformations to the smart filters request.
     * Subclasses can override this method to implement client-specific logic.
     *
     * @param smartFiltersRequest The smart filters request to transform
     * @param filterCountRequest The original filter count request
     * @param filterTitles List of filter titles
     */
    protected void applyClientSpecificTransformations(
            SmartFiltersRequest smartFiltersRequest, 
            FilterCountRequest filterCountRequest, 
            List<String> filterTitles) {
        
        // Default implementation - no additional transformations
        // Subclasses can override this to add client-specific transformations
    }
}
