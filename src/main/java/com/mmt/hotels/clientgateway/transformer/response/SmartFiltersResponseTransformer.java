package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.service.ListingService;
import com.mmt.hotels.clientgateway.util.ExceptionHandler;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.util.MetricErrorLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

public abstract class SmartFiltersResponseTransformer {

    protected static final Logger logger = LoggerFactory.getLogger(SmartFiltersResponseTransformer.class);


    @Autowired
    private MetricErrorLogger metricErrorLogger;


    /**
     * Converts filter tags and all filters to a SmartFiltersResponse based on client-specific requirements.
     * This method handles the transformation from filter tags (strings) to matching filters.
     *
     * @param filterTags List of filter tag strings
     * @param allFilters List of all available filters
     * @param filterCountRequest The original filter count request
     * @return SmartFiltersResponse with client-specific transformations applied
     */
    public SmartFiltersResponse convertSmartFiltersResponse(
            List<String> filterTags,
            List<com.mmt.hotels.clientgateway.response.filter.Filter> allFilters,
            FilterCountRequest filterCountRequest) {
        
        SmartFiltersResponse response = new SmartFiltersResponse();
        
        if (CollectionUtils.isNotEmpty(filterTags) && CollectionUtils.isNotEmpty(allFilters)) {
            // Transform filter tags to matching filters
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters = 
                getMatchingFilters(allFilters, filterTags);
            
            // Apply client-specific transformations
            List<com.mmt.hotels.clientgateway.response.filter.Filter> transformedFilters = 
                applyClientSpecificTransformations(matchingFilters, filterCountRequest);
            response.setFilters(transformedFilters);
        } else {
            response.setFilters(new ArrayList<>());
        }
        
        return response;
    }

    /**
     * Converts the matching filters to a SmartFiltersResponse based on client-specific requirements.
     * This is a convenience method for when matching filters are already available.
     *
     * @param matchingFilters List of matching filters
     * @param filterCountRequest The original filter count request
     * @return SmartFiltersResponse with client-specific transformations applied
     */
    public SmartFiltersResponse convertSmartFiltersResponse(
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters,
            FilterCountRequest filterCountRequest) {
        
        SmartFiltersResponse response = new SmartFiltersResponse();
        
        if (CollectionUtils.isNotEmpty(matchingFilters)) {
            // Apply client-specific transformations
            List<com.mmt.hotels.clientgateway.response.filter.Filter> transformedFilters = 
                applyClientSpecificTransformations(matchingFilters, filterCountRequest);
            response.setFilters(transformedFilters);
        } else {
            response.setFilters(new ArrayList<>());
        }
        
        return response;
    }
    
    /**
     * Apply client-specific transformations to the filters.
     * Subclasses can override this method to implement client-specific logic.
     *
     * @param matchingFilters List of matching filters
     * @param filterCountRequest The original filter count request
     * @return Transformed list of filters
     */
    protected List<com.mmt.hotels.clientgateway.response.filter.Filter> applyClientSpecificTransformations(
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters,
            FilterCountRequest filterCountRequest) {
        
        // Default implementation - return filters as-is
        // Subclasses can override this to add client-specific transformations
        return matchingFilters;
    }

    /**
     * Gets matching filters from the list of all filters based on filter tags.
     * This method finds the corresponding filter for each filter tag and adds it to the matching filters list.
     * Filters are sorted using a custom comparator that compares by filterGroup, filterValue, and range.
     *
     * @param filters List of all available filters
     * @param filterTags List of filter tag strings to match
     * @return List of matching filters
     */
    protected List<com.mmt.hotels.clientgateway.response.filter.Filter> getMatchingFilters(
            List<com.mmt.hotels.clientgateway.response.filter.Filter> filters, 
            List<String> filterTags) {
        
        Comparator<com.mmt.hotels.clientgateway.response.filter.Filter> filterGroupValueComparator = (filter1, filter2) -> {
            // 1. First compare by filterGroup
            String group1 = filter1.getFilterGroup();
            String group2 = filter2.getFilterGroup();
            int groupComparison = Objects.compare(group1, group2, Comparator.nullsLast(String::compareTo));
            if (groupComparison != 0) {
                return groupComparison;
            }
            
            // 2. If filterGroup is same, compare by filterValue if both are non-null
            String value1 = filter1.getFilterValue();
            String value2 = filter2.getFilterValue();
            
            if (value1 != null && value2 != null) {
                return value1.compareTo(value2);
            }
            
            // 3. If filterValue is null for both items, compare by range
            if (value1 == null && value2 == null) {
                com.mmt.hotels.clientgateway.response.filter.FilterRange range1 = filter1.getFilterRange();
                com.mmt.hotels.clientgateway.response.filter.FilterRange range2 = filter2.getFilterRange();
                
                if (range1 != null && range2 != null) {
                    // Compare by min value first, then max value
                    int minComparison = Integer.compare(range1.getMinValue(), range2.getMinValue());
                    if (minComparison != 0) {
                        return minComparison;
                    }
                    return Integer.compare(range1.getMaxValue(), range2.getMaxValue());
                } else {
                    // Handle null ranges - put null ranges last
                    if (range1 == null && range2 == null) {
                        return 0;
                    } else if (range1 == null) {
                        return 1;
                    } else {
                        return -1;
                    }
                }
            }
            
            // If one has filterValue and other doesn't, put null filterValue first
            return value1 == null ? -1 : 1;
        };
        
        List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters = new ArrayList<>();
        if (filters != null && CollectionUtils.isNotEmpty(filterTags)) {
            //find the corresponding filter for each filterTag and add it to the matchingFilters list
            for (String filterTag : filterTags) {
                boolean filterAdded = false;
                
                // Try to match with existing filters
                for (com.mmt.hotels.clientgateway.response.filter.Filter filter : filters) {
                    if (isMatchingFilter(filter, filterTag, matchingFilters, filterGroupValueComparator)) {
                        matchingFilters.add(filter);
                        filterAdded = true;
                        break;
                    }
                }
                
                // If no matching filter found, check if it's a price filter
                if (!filterAdded && isPriceFilterTag(filterTag)) {
                    com.mmt.hotels.clientgateway.response.filter.Filter priceFilter = createPriceFilterFromTag(filterTag);
                    if (priceFilter != null && !isPriceFilterAlreadyExists(priceFilter, matchingFilters)) {
                        matchingFilters.add(priceFilter);
                    }
                }
            }
        }
        return matchingFilters;
    }

    /**
     * Checks if a filter matches the given filter tag and is not already present in the matching filters list.
     * Uses the same custom title generation logic as the request transformer to ensure proper matching.
     *
     * @param filter The filter to check
     * @param filterTag The filter tag to match against
     * @param matchingFilters List of already matched filters
     * @param filterGroupValueComparator Comparator for checking duplicates
     * @return true if the filter matches and is not a duplicate, false otherwise
     */
    private boolean isMatchingFilter(
            com.mmt.hotels.clientgateway.response.filter.Filter filter,
            String filterTag,
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters,
            Comparator<com.mmt.hotels.clientgateway.response.filter.Filter> filterGroupValueComparator) {
        
        if (StringUtils.isEmpty(filter.getTitle())) {
            return false;
        }
        
        // Generate custom title using the same logic as request transformer
        String customTitle = generateCustomTitle(filter);
        
        // Check if the custom title matches the filter tag
        boolean titleMatches = customTitle.equalsIgnoreCase(filterTag);
        
        // Check if this filter is not already in the matching filters list
        boolean isNotDuplicate = matchingFilters.stream()
                .noneMatch(item -> filterGroupValueComparator.compare(item, filter) == 0);
        
        return titleMatches && isNotDuplicate;
    }

    /**
     * Generates custom title for filters based on their filter group.
     * Uses the same logic as the request transformer to ensure consistency.
     * For filter groups defined in CUSTOM_TITLE_FILTER_GROUPS,
     * adds the filter group as a prefix followed by colon and then the filter title.
     *
     * @param filter The filter to generate title for
     * @return Custom title for the filter
     */
    private String generateCustomTitle(com.mmt.hotels.clientgateway.response.filter.Filter filter) {
        String filterGroup = filter.getFilterGroup();
        String filterTitle = filter.getTitle();
        
        // Check if the filter group requires custom title generation
        if (Constants.CUSTOM_TITLE_FILTER_GROUPS.contains(filterGroup)) {
            return filterGroup + ":" + filterTitle;
        }
        
        // For other filters, return the title as-is
        return filterTitle;
    }

    /**
     * Checks if the given filter tag is a price filter tag.
     *
     * @param filterTag The filter tag to check
     * @return true if it's a price filter tag, false otherwise
     */
    private boolean isPriceFilterTag(String filterTag) {
        return StringUtils.isNotEmpty(filterTag) && 
               filterTag.startsWith(Constants.FILTER_PRICE_BUCKET_HES + ":");
    }

    /**
     * Creates a price filter object from a price filter tag.
     *
     * @param filterTag The price filter tag (format: "HOTEL_PRICE_BUCKET:₹minValue to ₹maxValue")
     * @return Price filter object or null if parsing fails
     */
    private com.mmt.hotels.clientgateway.response.filter.Filter createPriceFilterFromTag(String filterTag) {
        try {
            // Extract title after colon
            String title = filterTag.substring(Constants.FILTER_PRICE_BUCKET_HES.length() + 1);
            
            // Parse range from title (format: "₹minValue to ₹maxValue")
            com.mmt.hotels.clientgateway.response.filter.FilterRange filterRange = parseFilterRangeFromTitle(title);
            
            if (filterRange != null) {
                com.mmt.hotels.clientgateway.response.filter.Filter priceFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
                priceFilter.setFilterGroup(Constants.FILTER_PRICE_BUCKET_HES);
                priceFilter.setFilterValue(null);
                priceFilter.setTitle(title);
                priceFilter.setFilterRange(filterRange);
                priceFilter.setRangeFilter(true);
                return priceFilter;
            }
        } catch (Exception e) {
            logger.error("error occurred in createPriceFilterFromTag: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            // Log error or handle gracefully
        }
        return null;
    }

    /**
     * Parses filter range from title string.
     *
     * @param title The title string (format: "₹minValue to ₹maxValue")
     * @return FilterRange object or null if parsing fails
     */
    private com.mmt.hotels.clientgateway.response.filter.FilterRange parseFilterRangeFromTitle(String title) {
        try {
            // Remove ₹ symbol and split by " to "
            String cleanTitle = title.replace("₹", "").trim();
            String[] parts = cleanTitle.split(" to ");
            
            if (parts.length == 2) {
                int minValue = Integer.parseInt(parts[0].trim());
                int maxValue = Integer.parseInt(parts[1].trim());
                
                com.mmt.hotels.clientgateway.response.filter.FilterRange filterRange = new com.mmt.hotels.clientgateway.response.filter.FilterRange();
                filterRange.setMinValue(minValue);
                filterRange.setMaxValue(maxValue);
                return filterRange;
            }
        } catch (Exception e) {
            logger.error("error occurred in parseFilterRangeFromTitle: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            // Log error or handle gracefully
        }
        return null;
    }

    /**
     * Checks if a price filter with the same title already exists in the matching filters list.
     *
     * @param priceFilter The price filter to check
     * @param matchingFilters List of already matched filters
     * @return true if a price filter with same title exists, false otherwise
     */
    private boolean isPriceFilterAlreadyExists(
            com.mmt.hotels.clientgateway.response.filter.Filter priceFilter,
            List<com.mmt.hotels.clientgateway.response.filter.Filter> matchingFilters) {
        
        return matchingFilters.stream()
                .anyMatch(filter -> Constants.FILTER_PRICE_BUCKET_HES.equals(filter.getFilterGroup()) &&
                                  StringUtils.isNotEmpty(filter.getTitle()) &&
                                  filter.getTitle().equalsIgnoreCase(priceFilter.getTitle()));
    }

}
