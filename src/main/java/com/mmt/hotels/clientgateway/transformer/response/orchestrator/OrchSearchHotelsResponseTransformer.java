package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.model.request.common.LocationDetails;
import com.gommt.hotels.orchestrator.model.response.listing.*;
import com.gommt.hotels.orchestrator.model.response.ugc.ListingReviewDetails;
import com.gommt.hotels.orchestrator.model.response.ugc.SubRatingsDetails;
import com.google.gson.Gson;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.consul.CommonConfigConsul;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.Coupon;
import com.mmt.hotels.clientgateway.response.LocationDetail;
import com.mmt.hotels.clientgateway.response.LuckyData;
import com.mmt.hotels.clientgateway.response.LuckyUserDetails;
import com.mmt.hotels.clientgateway.response.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.dayuse.SlotDetail;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.response.searchHotels.SortCriteria;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.DateUtil;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import com.mmt.hotels.model.request.GuestCount;
import com.mmt.hotels.model.request.RoomStayCandidate;
import com.mmt.hotels.model.request.flyfish.Count;
import com.mmt.hotels.model.request.flyfish.FlyfishReviewData;
import com.mmt.hotels.model.request.flyfish.OTA;
import com.mmt.hotels.model.request.flyfish.TopicRating;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import com.mmt.hotels.model.response.dayuse.MissingSlotDetail;
import com.mmt.hotels.model.response.staticdata.TransportPoi;
import com.mmt.hotels.util.PromotionalOfferType;
import com.mmt.hotels.util.Tuple;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import javax.annotation.PostConstruct;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.Constants.MPN;
import static com.mmt.hotels.clientgateway.constants.ConstantsTranslation.*;
import static com.mmt.hotels.clientgateway.util.DateUtil.MMDDYYYY;


@Component
abstract public class OrchSearchHotelsResponseTransformer {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchSearchHotelsResponseTransformer.class);

    @Autowired
    CommonResponseTransformer commonResponseTransformer;

    @Autowired
    Utility utility;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    private DateUtil dateUtil;


    @Autowired
    MobConfigHelper mobConfigHelper;

    @Autowired
    CommonConfigConsul commonConfigConsul;

    @Value("${hotel.level.app.deeplink}")
    protected String hotelLevelAppDeepLink;

    @Value("${hotel.level.sharing.url}")
    protected String hotelLevelSharingUrl;

    @Value("${root.level.sharing.url}")
    protected String rootLevelSharingUrl;

    @Value("${root.level.deeplink.url}")
    protected String rootLevelDeeplink;

    @Value("${detail.deep.link.url}")
    protected String basicDetailDeeplink;

    @Value("${listing.deep.link.url}")
    protected String listinglDeeplink;

    @Value("${listing.app.deep.link.url}")
    protected String listingApplDeeplink;

    @Value("${listing.deep.link.url.global}")
    protected String listinglDeeplinkGlobal;

    @Value("${detail.deep.link.url.global}")
    protected String basicDetailDeeplinkGlobal;

    @Autowired
    private PropertyManager propManager;

    @Autowired
    CommonHelper commonHelper;

    public MissingSlotDetail missingSlotDetails = null;

    public int thresholdForSlashedAndDefaultHourPrice = 0;

    protected static Gson gson = new Gson();

    @PostConstruct
    public void init() {
        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        missingSlotDetails = commonConfig.missingSlotDetails();
        LOGGER.warn("missingSlotDetails : {}", missingSlotDetails);
        thresholdForSlashedAndDefaultHourPrice = commonConfig.thresholdForSlashedAndDefaultHourPrice();
    }

    protected abstract BottomSheet buildBottomSheet(PersonalizedSectionDetails perResponse);

    public abstract void addPersuasionHoverData(Hotel hotel, HotelDetails hotelEntity);

    public abstract void addSeoTextPersuasion(Hotel hotel, HotelDetails hotelEntity, boolean odd, ListingSearchRequest searchHotelsRequest, String sectionName);

    public abstract void addLocationPersuasionToHotelPersuasions(Hotel hotel, List<String> locationPersuasion, LinkedHashSet<String> facilities, ListingSearchRequest searchHotelsRequest, boolean enableAmenities, boolean sectionMyBizSimilarToDirectHtl, String dayUsePersuasionsText, TransportPoi nearestGroundTransportPoi, String drivingTimeText, LocationDetails locusData, boolean hcardV2);

    protected abstract String buildBGColor(String section, String orientation, String cardType);

    protected abstract void addBookingConfirmationPersuasion(HotelDetails hotelEntity);

    public abstract MyBizStaticCard buildStaticCard(String section, List<HotelDetails> hotels);

    public SearchHotelsResponse convertSearchHotelsResponse(ListingResponse listingResponse,
                                                            SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse) {
        SearchHotelsResponse searchHotelsResponse = new SearchHotelsResponse();
        try {
            searchHotelsResponse.setCurrency(searchHotelsRequest.getSearchCriteria().getCurrency());
            searchHotelsResponse.setHotelCount(listingResponse.getHotelCount());

            //searchHotelsResponse.setHotelCountInCity(listingPageResponseBO.getHotelCountInCity());
            searchHotelsResponse.setLastHotelCategory(listingResponse.getLastFetchedHotelCategory());
            if (searchHotelsRequest.getExpData() != null) {
                LinkedHashMap<String, String> expDataMap = commonModifierResponse.getExpDataMap();
                if (expDataMap.containsKey("nearbyFixes") && Boolean.parseBoolean(expDataMap.get("nearbyFixes")) || expDataMap.containsKey("emptyShopSolution") && Boolean.parseBoolean(expDataMap.get("emptyShopSolution"))) {
                    searchHotelsResponse.setLastFetchedHotelCategory(listingResponse.getLastFetchedHotelCategory());
                }
            }

            searchHotelsResponse.setLastHotelId(listingResponse.getLastHotelId());
            searchHotelsResponse.setLastFetchedWindowInfo(listingResponse.getLastFetchedWindowInfo());

            if (listingResponse.getLocation() != null) {
                LocationDetails locationDetails = listingResponse.getLocation();
                searchHotelsResponse.setLocationDetail(new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName()));
                LocationDetails cityLocationDetails = getCityLocationDetail(listingResponse);
                searchHotelsResponse.setCityLocationDetail(new LocationDetail(cityLocationDetails.getCityId(), cityLocationDetails.getCityName(), "city", null, null));
            }
            boolean noMoreHotels = (listingResponse.getLastPage() != null && listingResponse.getLastPage()) || StringUtils.isEmpty(listingResponse.getLastHotelId());
            searchHotelsResponse.setNoMoreHotels(noMoreHotels);

            String idContext = searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getIdContext() : null;
            searchHotelsResponse.setPersonalizedSections(buildPersonalizedSections(listingResponse.getPersonalizedSections(), searchHotelsRequest.getExpDataMap(), idContext, searchHotelsRequest, commonModifierResponse, listingResponse.getLocation()));
            searchHotelsResponse.setSortCriteria(buildSortCriteria(listingResponse.getSortCriteria()));
            searchHotelsResponse.setExpData(buildExpData(commonModifierResponse));
//            searchHotelsResponse.setUsradid(listingPageResponseBO.getUsradid());
            searchHotelsResponse.setHydraSegments(getHydraSegments(commonModifierResponse));
//            searchHotelsResponse.setUserLoyaltyStatus(listingPageResponseBO.getUserLoyaltyStatus());
//            searchHotelsResponse.setUserFareHold(listingPageResponseBO.isUserFareHold());

//            todo different implementation
//            if (listingPageResponseBO.getSectionsType() != null && SectionsType.NEARBY.equals(listingPageResponseBO.getSectionsType())) {
//                searchHotelsResponse.setSectionsType(listingPageResponseBO.getSectionsType().getValue());
//            }
//

            String listingSharingUrl = prepareListingSharingUrl(searchHotelsRequest, rootLevelSharingUrl, true, true,null);
            searchHotelsResponse.setSharingUrl(listingSharingUrl);

            if (StringUtils.isNotBlank(listingResponse.getSharingUrl())) {
                searchHotelsResponse.setSharingUrl(listingResponse.getSharingUrl());
            }

           /* if (CollectionUtils.isNotEmpty(listingPageResponseBO.getPaxDetailsList())) {
                searchHotelsResponse.setPaxDetails(listingPageResponseBO.getPaxDetailsList());
            }*/
            if (commonModifierResponse != null) {
                searchHotelsResponse.setExpData(commonModifierResponse.getExpDataMap());
                searchHotelsResponse.setVariantKey(commonModifierResponse.getVariantKey());
            }
            /*ADDING FILTER CRITERIA TO PRE APPLIED FILTERS*/
            searchHotelsResponse.setPreAppliedFilters(searchHotelsRequest.getFilterCriteria());
            searchHotelsResponse.setNearMeFilterPill(getNearMeFilterPill(searchHotelsRequest));
            searchHotelsResponse.setLuckyDataV2(buildLuckyDataV2(listingResponse.getLuckyCouponTimings()));
            searchHotelsResponse.setTickTockDealApplied(listingResponse.isOptedTickTock());
        } finally {
//            metricAspect.addToTimeInternalProcess(PROCESS_SEARCH_RESPONSE_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);
        }
        return searchHotelsResponse;
    }

    private LuckyData buildLuckyDataV2(Timings luckyCouponTimings) {
        if (luckyCouponTimings == null) {
            return null;
        }
        LuckyData luckyDataV2 = new LuckyData();
        LuckyUserDetails luckyUserDetails = new LuckyUserDetails();
        luckyUserDetails.setCurrent(luckyCouponTimings.getStartTime());
        luckyUserDetails.setEnd(luckyCouponTimings.getEndTime());
        luckyDataV2.setLuckyUserDetails(luckyUserDetails);
        luckyDataV2.setLuckyPersuasion(polyglotService.getTranslatedData(GI_LUCKY_TIMER_TEXT));
        return luckyDataV2;
    }


    private NearMeFilterPill getNearMeFilterPill(SearchHotelsRequest searchHotelsRequest) {
        // Near me filter pill to be shown in two cases:
        // 1. When user city is same as the search city.
        // 2. When user has explicitly searched "Properties Near Me".
        // In case user has explicitly searched "Properties Near Me", then filter pill to be shown in selected state.

        String searchedCity = searchHotelsRequest.getSearchCriteria() != null ?
                searchHotelsRequest.getSearchCriteria().getCityCode() : EMPTY_STRING;
        boolean userSearchedNearMe = searchHotelsRequest.getMatchMakerDetails() != null
                && CollectionUtils.isNotEmpty(searchHotelsRequest.getMatchMakerDetails().getLatLng())
                && Boolean.TRUE.equals(searchHotelsRequest.getMatchMakerDetails().getLatLng().get(0).getGiNearBySearch());
        boolean userCitySameAsSearchedCity = searchHotelsRequest.getUserLocation() != null
                && StringUtils.isNotBlank(searchHotelsRequest.getUserLocation().getCity())
                && searchHotelsRequest.getUserLocation().getCity().equalsIgnoreCase(searchedCity);
        if (userSearchedNearMe || userCitySameAsSearchedCity) {
            NearMeFilterPill nearMeFilterPill = new NearMeFilterPill();
            nearMeFilterPill.setNearMePillName(polyglotService.getTranslatedData(NEAR_ME_PILL_TITLE));
            nearMeFilterPill.setSelected(userSearchedNearMe);
            return nearMeFilterPill;
        }
        return null;
    }


    private LocationDetails getCityLocationDetail(ListingResponse listingResponse) {
        for (PersonalizedSectionDetails personalizedSectionDetails : listingResponse.getPersonalizedSections()) {
            for (HotelDetails hotel : personalizedSectionDetails.getHotels()) {
                return hotel.getLocation();
            }
        }
        return new LocationDetails();
    }


    private List<PersonalizedSection> buildPersonalizedSections(
            List<PersonalizedSectionDetails> personalizedSectionDetailsList, Map<String, String> expDataMap, String idContext, SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse, LocationDetails locusData) {
        if (CollectionUtils.isEmpty(personalizedSectionDetailsList)) {
            return null;
        }
        List<PersonalizedSection> personalizedSections = new ArrayList<>();
        Tuple<Boolean, String> directSearchHotelNameTup = new Tuple<>(Boolean.FALSE, "");

        //Fetch HotelId from Matchmaker Request
        String hotelId = Optional.of(searchHotelsRequest)
                .map(SearchHotelsRequest::getMatchMakerDetails)
                .map(MatchMakerRequest::getHotels)
                .filter(hotels -> !hotels.isEmpty())
                .map(hotels -> hotels.get(0))
                .map(InputHotel::getHotelId)
                .orElse("");

        //Fetch from Direct Hotel Section
        if (StringUtils.isNotEmpty(hotelId)) {
            String hotelName = personalizedSectionDetailsList.stream()
                    .filter(x -> DIRECT_HOTEL.equalsIgnoreCase(x.getName()))
                    .findFirst()
                    .map(PersonalizedSectionDetails::getHotels)
                    .filter(hotels -> !hotels.isEmpty())
                    .map(hotels -> hotels.get(0))
                    .map(HotelDetails::getName)
                    .orElse("");
            if (StringUtils.isNotEmpty(hotelName)) {
                directSearchHotelNameTup = new Tuple<>(Boolean.TRUE, hotelName);
            }
        }
        Tuple<Boolean, String> finalDirectSearchHotelNameTup = directSearchHotelNameTup;

        AtomicInteger similarSectionCounter = new AtomicInteger(0);
        personalizedSectionDetailsList.forEach(perResponse -> {
            PersonalizedSection personalizedSection = new PersonalizedSection();
            personalizedSection.setName(perResponse.getName());
            /*NO HEADINGS FOR GI, Only to show for Direct Search Case*/
//            personalizedSection.setHeading(perResponse.getHeading());
            if (!DIRECT_HOTEL.equalsIgnoreCase(perResponse.getName()) && (Boolean.TRUE.equals(finalDirectSearchHotelNameTup.getX()) || SIMILAR_HOTELS.equalsIgnoreCase(perResponse.getName())) && similarSectionCounter.get() < 1) {
                String hotelName = finalDirectSearchHotelNameTup.getY();
                if (StringUtils.isNotEmpty(hotelName)) {
                    personalizedSection.setHeading(SIMILAR_PROPERTIES_HEADING_DYNAMIC_TEXT + hotelName);
                } else {
                    // Handle the case where hotelName is null
                    personalizedSection.setHeading(SIMILAR_PROPERTIES_HEADING_DEFAULT);
                }
                similarSectionCounter.incrementAndGet();
            } else {
                personalizedSection.setHeading(perResponse.getHeading());
            }
            if (NEARBY_HOTELS_SECTION.equalsIgnoreCase(perResponse.getName())) {
                personalizedSection.setHeading(perResponse.getHeading());
            }
            personalizedSection.setSubHeading(perResponse.getSubHeading());
            personalizedSection.setHotels(buildPersonalizedHotels(perResponse.getHotels(), expDataMap, searchHotelsRequest, personalizedSection.getName(), commonModifierResponse, locusData));
            personalizedSection.setHotelCount(perResponse.getHotelCount());

            //Adding hotelCardType, seeMoreCTA, minHotelsToShow node here based newListingUi exp
            // if (StringUtils.isNotEmpty(perResponse.getHotelCardType()))
            //    personalizedSection.setHotelCardType(perResponse.getHotelCardType().toLowerCase());
           /* if (perResponse.getMinCardCount() != null) {
                personalizedSection.setMinHotelsToShow(Integer.valueOf(perResponse.getMinCardCount()));
            }
            if (StringUtils.isNotEmpty(perResponse.getSeeMoreCTA())) {
                personalizedSection.setSeeMoreCTA(perResponse.getSeeMoreCTA());
            }*/
//            personalizedSection.setShowMore(false);
            String orientation = buildOrientation(perResponse, expDataMap, searchHotelsRequest);
            personalizedSection.setOrientation(orientation);

            personalizedSections.add(personalizedSection);
        });
        return personalizedSections;
    }

    //Todo See if relevant
    private String buildOrientation(boolean horizontal) {
        if (horizontal) {
            return Constants.HORIZONTAL;
        } else {
            return Constants.VERTICAL;
        }
    }

    private String buildOrientation(PersonalizedSectionDetails personalizedResponse, Map<String, String> expDataMap, SearchHotelsRequest searchHotelsRequest) {
        boolean isMainFunnel = FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource());
        if (utility.isExperimentTrue(expDataMap, EXP_MOSTBOOK_ORIENTATION) && (isMainFunnel) && (Constants.MOST_BOOKED_HOTELS.equalsIgnoreCase(personalizedResponse.getName()))) {
            return Constants.HORIZONTAL;
        } else {
            return  personalizedResponse.getOrientation();
        }



    }

    /**
     * @param searchHotelsRequest can be null when called from buildComparatorResponse in StaticDetailResponseTransformer
     *                            adding null check to searchHotelsRequest is mandatory to avoid NPE
     * @param sectionName         Hotels section name
     * @throws NullPointerException if null checks are not added for searchHotelsRequest
     */
    public List<Hotel> buildPersonalizedHotels(List<HotelDetails> hotelList, Map<String, String> expDataMap, SearchHotelsRequest searchHotelsRequest, String sectionName,
                                               CommonModifierResponse commonModifierResponse, LocationDetails locusData) {
        if (CollectionUtils.isEmpty(hotelList)) {
            return null;
        }
        try {

            List<Hotel> hotels = new ArrayList<>();
            final boolean[] odd = {true};
            hotelList.forEach(hotelEntity -> {
                        Hotel hotel = new Hotel();
                        hotel.setGroupBookingHotel(hotelEntity.isGroupBookingHotel());
                        hotel.setGroupBookingPrice(hotelEntity.isGroupBookingPrice());
//                hotel.setIsGroupBookingForSimilar(hotelEntity.isGroupBookingHotel());
                hotel.setId(hotelEntity.getId());
                hotel.setGiId(hotelEntity.getGiId());
                hotel.setSpotlightApplicable(hotelEntity.isSpotlightApplicable());
                hotel.setName(hotelEntity.getName());
                hotel.setPropertyType(hotelEntity.getPropertyType());
                hotel.setPropertyLabel(hotelEntity.getPropertyLabel());
                hotel.setStayType(hotelEntity.getStayType());
                hotel.setStarRating(hotelEntity.getStarRating());
//                hotel.setFromCity(hotelEntity.getFromCity());
//                hotel.setDistance(hotelEntity.getDistance());
//                hotel.setCrossSellTag(hotelEntity.getCrossSellTag());
//                hotel.setDistanceUnit(hotelEntity.getDistanceUnit());
//                hotel.setFreeCancellationText("");
                        hotel.setSoldOut(hotelEntity.getSoldOutInfo() != null && StringUtils.isNotEmpty(hotelEntity.getSoldOutInfo().getType()));
                        hotel.setSoldOutInfo(buildSoldOutInfo(hotelEntity.getSoldOutInfo()));

//                hotel.setAlternateDates(hotelEntity.isAlternateDatesAvailable());
//                hotel.setMultiRoomRecommendation(hotelEntity.isRecommendedMultiRoom());
                        hotel.setIsAltAcco(hotelEntity.isAltAcco());
//                hotel.setMtKey(hotelEntity.getMtkey()); todo maybe
                        hotel.setGeoLocation(commonResponseTransformer.buildGeoLocation(hotelEntity.getLocation().getGeo()));
//                hotel.setDeepLink(hotelEntity.getDesktopDeeplink());
                       String detailUrl = getDetailDeeplink(searchHotelsRequest);
                        hotel.setAppDeeplink(buildHotelLevelAppDeepLink(searchHotelsRequest, hotel, null, null,null, locusData, detailUrl));
                        hotel.setSeoUrl(hotelEntity.getSeoUrl());
                        //Todo GI From Content for SEO FUNNEL ONLY
                        hotel.setShortDescSeo(hotelEntity.getShortDescSeo());
                        hotel.setLocationPersuasion(hotelEntity.getLocationPersuasions());

                        hotel.setMedia(commonResponseTransformer.buildMedia(hotelEntity.getMedia(), expDataMap));
                        hotel.setTotalImageCount(CollectionUtils.isNotEmpty(hotel.getMedia()) ? hotel.getMedia().size() : 0);
                        hotel.setTravellerImageCount(0);


                        String funnelSource = searchHotelsRequest.getRequestDetails().getFunnelSource();
                        /*  Todo DayUse*/
                        if (FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource)) {
                            hotel.setSlotDetail(buildSlotDetails(hotelEntity, searchHotelsRequest));
                        }
//                START - Build Price Detail
                        if ((FUNNEL_DAYUSE.equalsIgnoreCase(funnelSource) && hotelEntity.getRooms() != null)) {
                            //PRICE DETAILS ::
                            ResponseRatePlan responseRatePlan = Optional.ofNullable(hotelEntity.getRooms())
                                    .flatMap(rooms -> rooms.stream().findFirst())
                                    .flatMap(room -> Optional.ofNullable(
                                                    room.getRatePlans())
                                            .flatMap(ratePlans -> ratePlans.stream()
                                                    .filter(ratePlan -> ratePlan.getSlot() == null || ratePlan.getSlot().getDuration() == 0)
                                                    .findFirst()))
                                    .orElse(null);

                            PriceDetail priceDetail = Optional.ofNullable(responseRatePlan)
                                    .map(ResponseRatePlan::getPrice)
                                    .orElse(null);


                            if (shouldSetPriceDetailsForDayUse(hotel.getSlotDetail(), priceDetail)) {
                                hotel.setPriceDetail(buildPriceDetailForDayUse(priceDetail));
                            }
                        } else {
                        //PRICE DETAILS ::
                        ResponseRatePlan responseRatePlan = Optional.ofNullable(hotelEntity.getRooms())
                                .flatMap(rooms -> rooms.stream().findFirst())
                                .flatMap(room -> Optional.ofNullable(room.getRatePlans()).flatMap(ratePlans -> ratePlans.stream().findFirst()))
                                .orElse(null);

                        PriceDetail priceDetail = Optional.ofNullable(responseRatePlan)
                                .map(ResponseRatePlan::getPrice)
                                .orElse(null);
                        //Total Room Count is required to calculate price suffix
                        int totalRoomCount = hotelEntity.getTotalRoomCount() != 0 ? hotelEntity.getTotalRoomCount() : 1;
                        String priceSuffix = utility.getPriceSuffix(hotelEntity.isAltAcco(), totalRoomCount, searchHotelsRequest);

                        List<String> groupPriceSavingText = getGroupPriceAndSavingText(hotelEntity, searchHotelsRequest, responseRatePlan);
                        hotel.setPriceDetail(buildPriceDetail(responseRatePlan, false, commonResponseTransformer.enableDiscount(priceDetail),
                                searchHotelsRequest.getRequestDetails().isMetaInfo(), groupPriceSavingText, priceSuffix, TAXES_AND_FEES_TEXT));
                    }

                hotel.setHotelPersuasions(hotelEntity.getHotelPersuasions());
                LocationDetails locationDetails = hotelEntity.getLocation();
                hotel.setLocationDetail(new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName()));

                hotel.setCategories(hotelEntity.getCategories());

//                hotel.setShortList(hotelEntity.isShortList());
//                hotel.setNewType(hotelEntity.isNewType()); //"NEW" is the tag type which will be sent by singularity for reference (HTL-37120)
//                hotel.setPoiTag(hotelEntity.getPoiTag());
                hotel.setTotalRoomCount(hotelEntity.getTotalRoomCount());
                hotel.setPerNightTitle(commonHelper.buildPerNightPriceDescription(hotelEntity.getTotalRoomCount()));
//                hotel.setExtraMeals(hotelEntity.getExtraMeals());
//                hotel.setViewType(hotelEntity.getViewType());
//                hotel.setRatePersuasionText(hotelEntity.getRatePersuasionText());
                hotel.setCalendarCriteria(buildCalendarCriteria(hotelEntity.getCalendarCriteria()));


                String dayUsePersuasionText = ""; //TODO - hotelEntity.getDayUsePersuasionsText();
                TransportPoi nearestGroundPoi = null; //TODO - hotelEntity.getNearestGroundTransportPoi();
                String drivingTimeText = "";
                addLocationPersuasionToHotelPersuasions(hotel, hotelEntity.getLocationPersuasions(), hotelEntity.getFacilityHighlights(), searchHotelsRequest, true, false, dayUsePersuasionText, nearestGroundPoi, drivingTimeText, locusData, commonResponseTransformer.getHCARDV2(searchHotelsRequest.getExpData()));
                addPersuasionHoverData(hotel, hotelEntity);
//                hotel.setLastBookedInfo(hotelEntity.getLastBookedInfo());
//                hotel.setHotelBottomCard(buildHotelBottomCard(hotelEntity.getQuickBookInfo()));
//                hotel.setHotelTopCard(buildHotelTopCard(hotelEntity.getMyBizSimilarToDirectObj()));
//                hotel.setReviewDeeplinkUrl(hotelEntity.getReviewDeeplinkUrl());
//                hotel.setSearchRoomDeeplinkUrl(hotelEntity.getSearchRoomDeeplinkUrl());
//                hotel.setDetailDeeplinkUrl(hotelEntity.getDetailDeeplinkUrl());
                hotel.setHeroImage(hotelEntity.getHeroImage());
                hotel.setTrackingInfo(getUpdatedTrackingNode(hotelEntity.getTrackingInfo(), expDataMap));
                hotel.setSponsored(hotelEntity.getOmnitureFlags() != null && hotelEntity.getOmnitureFlags().isSponsored());

               /* if (hotelEntity.getCollectionCardPersuasion() != null) {
                    hotel.setCollectionCardPersuasion(new CollectionCardPersuasion());
                    hotel.getCollectionCardPersuasion().setText(hotelEntity.getCollectionCardPersuasion().getText());
                    hotel.getCollectionCardPersuasion().setIconUrl(hotelEntity.getCollectionCardPersuasion().getIconUrl());
                }*/
//                hotel.setMmtHotelCategory(hotelEntity.getMmtHotelCategory());
//                hotel.setWishListed(hotelEntity.isWishListed());
//                hotel.setStoryViewDescription(hotelEntity.getLongTailStoryPersuasions());
//                hotel.setPropertyHighlightText(hotelEntity.getLongTailPropertyCardPersuasions());
//                hotel.setGiReviewData(hotelEntity.getGiReviewData());
                //TODO MAYBE
//                hotel.setFacilitiesConfigMap(hotelEntity.getFacilitiesConfigMap());

//                hotel.setPastBookingRating(hotelEntity.getPastBookingRating());
//                hotel.setDateOfPreviousStay(hotelEntity.getDateOfPreviosStay());

                hotel.setReviewSummaryUgc(buildReviewSummary(hotelEntity.getReviewDetails(), commonModifierResponse));

//                hotel.setBookingCount(hotelEntity.getBookingCount());

                hotel.setFacilityHighlights(hotelEntity.getFacilityHighlights());

                // Based on this feature flag need to suppress few persuasions at specific placeholders configured at PMS and provided by client
                //At PMS level maintain map of key and List or PlaceHolders to suppress to make it generic if in future we want to suppress different placeholder for different flow we can use the same map
                /*if (searchHotelsRequest != null && searchHotelsRequest.getFeatureFlags() != null && searchHotelsRequest.getFeatureFlags().isPersuasionSuppression()) {
                    Map<String, List<String>> placeholdersToShowMap = null;
                    List<String> placeholdersToShow = null;
                    try {
                        //Not Using PMS config here, as it will be deprecated
                        placeholdersToShowMap = objectMapperUtil.getObjectFromJsonWithType(placeHoldersToShowConfig, new TypeReference<Map<String, List<String>>>() {
                                },
                                DependencyLayer.CLIENTGATEWAY);
                    } catch (JsonParseException e) {
                        e.printStackTrace();
                    }
                    if (MapUtils.isNotEmpty(placeholdersToShowMap)) {
                        placeholdersToShow = placeholdersToShowMap.get(SIMILAR_HOTELS);
                    }
                    LOGGER.debug("Placeholder we want to show for PersuasionSuppression {}", placeholdersToShow);
                    if (CollectionUtils.isNotEmpty(placeholdersToShow)) {
                        Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
                        List<String> placeholdersToBeBlocked = new ArrayList<>();
                        for (Object key : hotelPersuasions.keySet()) {
                            if (!placeholdersToShow.contains(key)) {
                                placeholdersToBeBlocked.add(key.toString());
                            }
                        }
                        LOGGER.debug("Placeholder we have to block from Persuasion for PersuasionSuppression {}", placeholdersToBeBlocked);
                        for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
                            if (MapUtils.isNotEmpty(hotelPersuasions)) {
                                hotelPersuasions.remove(placeholderToBeBlocked);
                            }
                        }
                    }
                }

*/
         /*       String bookingDevice = (searchHotelsRequest != null && searchHotelsRequest.getDeviceDetails() != null) ? searchHotelsRequest.getDeviceDetails().getBookingDevice() : null;
                if (MYBIZ_SIMILAR_TO_DIRECT_HOTEL.equalsIgnoreCase(sectionName) && (DEVICE_IOS.equalsIgnoreCase(bookingDevice)
                        || DEVICE_OS_ANDROID.equalsIgnoreCase(bookingDevice) || DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice))) {
                    List<String> placeholdersToBeBlocked = DEVICE_OS_DESKTOP.equalsIgnoreCase(bookingDevice) ? desktopPersPlaceholdersToBeBlockedDemandConc : appsPersPlaceholdersToBeBlockedDemandConc;
                    Map<Object, Object> hotelPersuasions = (Map<Object, Object>) hotel.getHotelPersuasions();
                    for (String placeholderToBeBlocked : placeholdersToBeBlocked) {
                        if (MapUtils.isNotEmpty(hotelPersuasions)) {
                            hotelPersuasions.remove(placeholderToBeBlocked);
                        }
                    }

                }
                commonResponseTransformer.buildSelectiveHotelPersuasions(hotel, hotelEntity);*/

                //For GI-ALL, B2C Hotels Funnel Except Direct Search Will be part of Similar Hotels
                if (commonHelper.checkValidHotel(searchHotelsRequest, hotelEntity)) {
//                    try {
                    if (searchHotelsRequest.getSearchCriteria() != null
                            && searchHotelsRequest.getRequestDetails().getIdContext() != null
                            && searchHotelsRequest.getRequestDetails().getFunnelSource() != null
                            && searchHotelsRequest.getSearchCriteria().getLocationType() != null
                            && B2C.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext())
                            && FUNNEL_SOURCE_HOTELS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())
                            && !(Constants.ZONE).equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getLocationType()))
                        //For GI-ALL Hotels Except Direct Search Will be part of Similar Hotels.
                        hotel.setSimilarHotelsRequired(true);
                    //TO DO FUTURE USE
                    // Based on this node client will hit search-hotel api similarHotel flow, and we set this flag based on some conditions provide by Product and configured at PMS to change them dynamically
                    //HTL-39243 Supress SimilarHotels true for Direct Searched Property
//                            hotel.setSimilarHotelsRequired(getSimilarHotelRequired(hotelEntity));


//                    } catch (JsonParseException e) {
//                        e.printStackTrace();
//                    }
                }

                odd[0] = !odd[0];
                hotels.add(hotel);
            });

            return hotels;
        } catch (Exception e) {
            LOGGER.error("An exception occurred in buildPersonalizedHotels ", e);
            return null;
        }
    }

    private JsonNode getUpdatedTrackingNode(JsonNode trackingInfoJsonNode, Map<String, String> expDataMap){
        if (trackingInfoJsonNode == null) {
            return trackingInfoJsonNode;
        }

        try {
            // Create a mutable copy of the JsonNode
            boolean shouldOverwrite = shouldOverwriteViewTrackUrl(expDataMap);
            if(shouldOverwrite){
                com.fasterxml.jackson.databind.node.ObjectNode mutableNode = trackingInfoJsonNode.deepCopy();

                // Check if tracking_node exists
                if (mutableNode.has("tracking_node") && mutableNode.get("tracking_node").isObject()) {
                    com.fasterxml.jackson.databind.node.ObjectNode trackingNode =
                            (com.fasterxml.jackson.databind.node.ObjectNode) mutableNode.get("tracking_node");

                    // Set click_url and view_url to null
                    trackingNode.putNull("click_url");
                    trackingNode.putNull("view_url");
                }

                return mutableNode;
            }else{
                //TODO: will be removed after next two releases of ios (Tentative Aug 25)
                com.fasterxml.jackson.databind.node.ObjectNode mutableNode = trackingInfoJsonNode.deepCopy();
                if (mutableNode.has("tracking_node") && mutableNode.get("tracking_node").isObject()) {
                    mutableNode.set("trackingNode", mutableNode.get("tracking_node"));
                }
                return mutableNode;
            }
        } catch (Exception e) {
            LOGGER.error("Error updating tracking node", e);
            return trackingInfoJsonNode;
        }
    }

    private boolean shouldOverwriteViewTrackUrl(Map<String, String> expDataMap){
        return expDataMap!=null && expDataMap.containsKey(GI_SL_TRACKING_PARITY) && utility.isExperimentTrue(expDataMap, GI_SL_TRACKING_PARITY);
    }


    protected com.mmt.hotels.clientgateway.response.PriceDetail buildPriceDetailForDayUse(PriceDetail input) {
        if (input == null) {
            return null;
        }
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        priceDetail.setPrice(input.getDisplayPrice());
        priceDetail.setPriceWithTax(Math.floor(input.getDisplayPrice()));
        priceDetail.setDiscountedPrice(Math.floor(input.getDisplayPrice()));
        priceDetail.setDiscountedPriceWithTax(Math.floor(input.getDisplayPrice() + input.getTotalTax()));
        priceDetail.setTotalTax(input.getTotalTax());
        priceDetail.setPricingKey("DEFAULT");
        priceDetail.setPriceSuffix(DAY_USE_ONE_NIGHT_PRICE);
        priceDetail.setTaxesAndFeesText(TAXES_AND_FEES_TEXT);

        return priceDetail;
    }


    // HTL-37846 => dont send price detail or slashed price for day use if it is less than hourly prices 3h/6h/9h
    public boolean shouldSetPriceDetailsForDayUse(List<SlotDetail> slotDetails, PriceDetail input) {
        if (CollectionUtils.isNotEmpty(slotDetails) && input != null) {
            for (SlotDetail slotDetail : slotDetails) {

               double fullNightPrice = input.getDisplayPrice() + input.getTotalTax();
                if (slotDetail != null && !isSlashedPriceGreaterThanHourlyPrice(slotDetail,fullNightPrice)) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean isSlashedPriceGreaterThanHourlyPrice(SlotDetail slotDetail, double fullNightPrice) {
        if(slotDetail != null && slotDetail.getPriceDetail() == null)
            return true;
        return (slotDetail.getPriceDetail().getDiscountedPriceWithTax() < fullNightPrice);
    }


    private List<SlotDetail> buildSlotDetails(HotelDetails hotelEntity, SearchHotelsRequest searchHotelsRequest) {
        if (hotelEntity == null || CollectionUtils.isEmpty(hotelEntity.getRooms())) {
            return null;
        }
        List<SlotDetail> slotDetailList = new ArrayList<>();
        List<RoomEntity> roomTypeDetailsList = hotelEntity.getRooms();
        Set<Integer> slotDetailCount = new HashSet<>();
        Slot slot = null;
        SlotDetail slotDetail = null;
        String slotTime = null;

        // If a particular select is requested return that slot only
        if (searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null && searchHotelsRequest.getSearchCriteria().getSlot() != null
                && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() != null && searchHotelsRequest.getSearchCriteria().getSlot().getDuration() > 0) {
            Integer requestedSlotDuration = searchHotelsRequest.getSearchCriteria().getSlot().getDuration();
            for (RoomEntity roomTypeDetails : roomTypeDetailsList) {
                List<ResponseRatePlan> ratePlans = roomTypeDetails.getRatePlans();
                for(ResponseRatePlan ratePlan:ratePlans){
                    if (ratePlan.getSlot() != null && ratePlan.getSlot().getDuration()>0
                            && requestedSlotDuration == ratePlan.getSlot().getDuration()) {
                        buildSlotDetailList(hotelEntity, searchHotelsRequest, slotDetailList, slotDetailCount, ratePlan);
                    }
                }
            }
            return slotDetailList;
        }

       /* protected com.mmt.hotels.clientgateway.response.PriceDetail buildPriceDetailForDayUse( displayFare) {
            if (displayFare == null) {
                return null;
            }
            com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
            priceDetail.setTotalTax(displayFare.getTax() != null ? displayFare.getTax().getValue() : 0);
            if (displayFare.getSlashedPrice() != null) {
                priceDetail.setPrice(Math.floor(displayFare.getSlashedPrice().getSellingPriceNoTax()));
                priceDetail.setPriceWithTax(Math.floor(displayFare.getSlashedPrice().getSellingPriceWithTax()));
                priceDetail.setDiscountedPrice(Math.floor(displayFare.getSlashedPrice().getSellingPriceNoTax()));
                priceDetail.setDiscountedPriceWithTax(Math.floor(displayFare.getSlashedPrice().getSellingPriceWithTax()));
                priceDetail.setPriceSuffix(DAY_USE_ONE_NIGHT_PRICE);
            }
            return priceDetail;
        }*/

        for (RoomEntity roomTypeDetails : roomTypeDetailsList) {
            List<ResponseRatePlan> ratePlans = roomTypeDetails.getRatePlans();
            for(ResponseRatePlan ratePlan:ratePlans){
            if (ratePlan.getSlot() != null && ratePlan.getSlot().getDuration() >0) {
                slotTime = buildSlotDetailList(hotelEntity, searchHotelsRequest, slotDetailList, slotDetailCount, ratePlan);
            }
        }}

        if (!slotDetailCount.isEmpty() && slotDetailCount.size() < 3) {
            Iterator<Integer> itr = missingSlotDetails != null && missingSlotDetails.getDuration() != null ? missingSlotDetails.getDuration().iterator() : null;
            while (itr != null && itr.hasNext()) {
                Integer value = itr.next();
                if (!slotDetailCount.contains(value)) {
                    slotDetail = new SlotDetail();
                    slot = new Slot();
                    slot.setDuration(value);
                    com.mmt.hotels.model.response.dayuse.Slot tempSlot = new com.mmt.hotels.model.response.dayuse.Slot();
                    tempSlot.setDuration(slot.getDuration());
                    tempSlot.setTimeSlot(slotTime);
                    slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(tempSlot));
                    slotDetail.setSlot(slot);
                    slotDetailList.add(slotDetail);
                }
            }
        }
        return slotDetailList;
    }


    private String buildSlotDetailList(HotelDetails hotelEntity, ListingSearchRequest searchHotelsRequest, List<SlotDetail> slotDetailList,
                                       Set<Integer> slotDetailCount, ResponseRatePlan ratePlan) {
        Slot slot;
        SlotDetail slotDetail;
        String slotTime;
        slotDetail = new SlotDetail();

        List<String> groupPriceSavingText = getGroupPriceAndSavingText(hotelEntity, searchHotelsRequest, ratePlan);
        slotDetail.setPriceDetail(buildPriceDetail(ratePlan,
                commonResponseTransformer.enableSaveValue(searchHotelsRequest.getExpData()),
                commonResponseTransformer.enableDiscount( ratePlan.getPrice()  != null ? ratePlan.getPrice()  : null),
                (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().isMetaInfo()),
                groupPriceSavingText, PN_PRICE_SUFFIX,PRICES_TEXT_DAYUSE));
        slot = new Slot();
        slot.setDuration(ratePlan.getSlot().getDuration());
        slotDetailCount.add(slot.getDuration());
        slotTime = ratePlan.getSlot().getTimeSlot();
        slot.setTimeSlot(utility.calculateTimeSlot_Meridiem(slotTime, slot.getDuration()));
        slotDetail.setSlot(slot);
        slotDetailList.add(slotDetail);
        return slotTime;
    }


    private List<String> getGroupPriceAndSavingText(HotelDetails searchWrapperHotelEntity, ListingSearchRequest listingSearchRequest, ResponseRatePlan responseRatePlan) {

        List<String> groupPriceAndSavingText = new ArrayList<>();

        if (responseRatePlan == null) {
            return groupPriceAndSavingText;
        }

        long displayPrice = (long) responseRatePlan.getPrice().getDisplayPrice();
        long savingPerc = (long) responseRatePlan.getPrice().getSavingPerc();
        int roomCount = searchWrapperHotelEntity.getTotalRoomCount();


        String checkIn = null;
        String checkOut = null;
        if (listingSearchRequest != null && listingSearchRequest.getSearchCriteria() != null) {
            checkOut = listingSearchRequest.getSearchCriteria().getCheckOut();
            checkIn = listingSearchRequest.getSearchCriteria().getCheckIn();
        }
        int nightCount = 0;
        if (StringUtils.isNotBlank(checkIn) && StringUtils.isNotBlank(checkOut)) {
            nightCount = dateUtil.getDaysDiff(LocalDate.parse(checkIn), LocalDate.parse(checkOut));
        }

        if (StringUtils.equalsIgnoreCase(searchWrapperHotelEntity.getStayType(), "ENTIRE") && (
                responseRatePlan.getPrice().getTotalRoomCount() != null && responseRatePlan.getPrice().getTotalRoomCount() == 1)) {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en", "IN"));
            groupPriceAndSavingText.add(polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(GROUP_PRICE_TEXT_ONE_NIGHT, listingSearchRequest != null ? listingSearchRequest.getClient() : ""))
                    .replace(ConstantsTranslation.AMOUNT.toUpperCase(), numberFormat.format(displayPrice))
                    .replace("{NIGHT_COUNT}", String.valueOf(nightCount))
                    .replace("{ROOM_COUNT}", String.valueOf(roomCount)));
        } else {
            NumberFormat numberFormat = com.ibm.icu.text.NumberFormat.getInstance(new Locale("en", "IN"));
            groupPriceAndSavingText.add(commonResponseTransformer.getGroupPriceText(roomCount, nightCount, numberFormat.format(displayPrice), listingSearchRequest != null ? listingSearchRequest.getClient() : ""));
        }


        String savingPercText = null;
        if (savingPerc != 0.0 && searchWrapperHotelEntity.isGroupBookingPrice()) {
            savingPercText = polyglotService.getTranslatedData(Utility.getGroupPriceTextOrSavingPercKey(SAVING_PERC_TEXT, listingSearchRequest != null ? listingSearchRequest.getClient() : "")).replace("{PERCENTAGE}", String.valueOf(savingPerc));
        }
        groupPriceAndSavingText.add(savingPercText);

        return groupPriceAndSavingText;
    }


    public FlyfishReviewData buildReviewSummary(ListingReviewDetails reviewSummaryUgc, CommonModifierResponse commonModifierResponse) {
        if (reviewSummaryUgc != null) {
            FlyfishReviewData flyfishReviewData = new FlyfishReviewData();
            //Todo Filtered Rating Count
//            flyfishReviewData.setFilteredReviewCount(reviewSummaryUgc.getFilteredReviewCount());
            flyfishReviewData.setHotelRating(reviewSummaryUgc.getRating());
            flyfishReviewData.setReviewCount(reviewSummaryUgc.getTotalReviewCount());
            flyfishReviewData.setIsNewListing(reviewSummaryUgc.getIsNewListing());
            flyfishReviewData.setOta(changeUGCDataSource(reviewSummaryUgc.getOta(), commonModifierResponse));
            flyfishReviewData.setRatingCount(reviewSummaryUgc.getTotalRatingCount());
            flyfishReviewData.setTopicRatings(buildTopicRatings(reviewSummaryUgc.getSubRatings()));
            if (MapUtils.isNotEmpty(reviewSummaryUgc.getRatingWiseCount())) {
                Map<String, Count> ratingWiseCount = new HashMap<>();
                reviewSummaryUgc.getRatingWiseCount().forEach((key, count) -> {
                    ratingWiseCount.put(key, buildCountObject(count));
                });
                if (MapUtils.isNotEmpty(ratingWiseCount)) {
                    flyfishReviewData.setRatingWiseCount(ratingWiseCount);
                }
            }

           /*Todo if Required
               if (CollectionUtils.isNotEmpty(reviewSummaryUgc.getTopicRatings())) {
                flyfishReviewData.setTopicRatings(reviewSummaryUgc.getTopicRatings().stream()
                        .filter(Objects::nonNull)
                        .map(topicRatings -> {
                            TopicRating topicRating = new TopicRating();
                            topicRating.setRating(topicRatings.getRating());
                            topicRating.setTitle(topicRatings.getTitle());
                            return topicRating;
                        })
                        .collect(Collectors.toCollection(ArrayList::new)));
            }*/
            return flyfishReviewData;
        }
        return null;
    }

    private String changeUGCDataSource(String currentOTA, CommonModifierResponse commonModifierResponse) {
        if (!StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, COMBINED_OTA_FLOW), TRUE)) {
            if (StringUtils.isNotBlank(currentOTA) && (StringUtils.equalsIgnoreCase(currentOTA, OTA.GI_EXP.getValue()) || StringUtils.equalsIgnoreCase(currentOTA, OTA.GI_BKG.getValue()))) {
                return OTA.GI.getValue();
            }
        }
        return currentOTA;
    }

    private ArrayList<TopicRating> buildTopicRatings(List<SubRatingsDetails> subRatings) {
        if (CollectionUtils.isNotEmpty(subRatings)) {
            return subRatings.stream()
                    .filter(Objects::nonNull)
                    .map(subRating -> {
                        TopicRating topicRating = new TopicRating();
                        topicRating.setRating(subRating.getRating());
                        topicRating.setTitle(subRating.getName());
                        return topicRating;
                    })
                    .collect(Collectors.toCollection(ArrayList::new));
        }
        return null;
    }


    private Count buildCountObject(com.gommt.hotels.orchestrator.model.response.ugc.Count count) {
        Count countObj = new Count();
        countObj.setCount(count.getCount());
        return countObj;
    }


    private CalendarCriteria buildCalendarCriteria(com.gommt.hotels.orchestrator.model.response.content.CalendarCriteria calendarCriteriaHES) {
        if (calendarCriteriaHES == null)
            return null;
        CalendarCriteria calendarCriteriaCG = new CalendarCriteria();
        calendarCriteriaCG.setAdvanceDays(calendarCriteriaHES.getAdvanceDays());
        calendarCriteriaCG.setAvailable(calendarCriteriaHES.isAvailable());
        calendarCriteriaCG.setMaxDate(calendarCriteriaHES.getMaxDate());
        calendarCriteriaCG.setMlos(calendarCriteriaHES.getMlos());
        return calendarCriteriaCG;
    }


    private com.mmt.hotels.clientgateway.response.PriceDetail buildPriceDetail(ResponseRatePlan responseRatePlan, boolean enableSaveValue, boolean enableDiscount,
                                                                               boolean metaInfo, List<String> groupPriceAndSavingText, String priceSuffix,String taxAndFeeText) {
        if (responseRatePlan == null) {
            return null;
        }
        PriceDetail priceDetailOrch = responseRatePlan.getPrice();
        String lowestRateSegmentId = ""; //responseRatePlan.getLowestRateSegmentId();
        com.mmt.hotels.clientgateway.response.PriceDetail priceDetail = new com.mmt.hotels.clientgateway.response.PriceDetail();
        if (CollectionUtils.isNotEmpty(groupPriceAndSavingText)) {
            //TODO -Not found in Android contract
//            priceDetail.setGroupPriceText(groupPriceAndSavingText.get(0));
            priceDetail.setSavingsText(groupPriceAndSavingText.get(1));
        }

//        priceDetail.setEmiDetails(buildEmiDetails(displayPriceBreakDown.getEmiDetails()));

        priceDetail.setCoupon(buildCoupon(priceDetailOrch));

        priceDetail.setTotalTax(priceDetailOrch.getTotalTax());

        double saving = priceDetailOrch.getHotelDiscount() + priceDetailOrch.getCouponDiscount();
        if (enableSaveValue) {
            priceDetail.setTotalSaving(saving);
            priceDetail.setSavingPerc(priceDetailOrch.getSavingPerc());
        }


        priceDetail.setPrice(priceDetailOrch.getBasePrice());
        priceDetail.setPriceWithTax(priceDetailOrch.getBasePrice() + priceDetailOrch.getTotalTax());
        priceDetail.setDiscountedPrice(priceDetailOrch.getDisplayPrice());
        priceDetail.setDiscountedPriceWithTax(priceDetailOrch.getDisplayPrice() + priceDetailOrch.getTotalTax());
        priceDetail.setTotalTax(priceDetailOrch.getTotalTax());
        priceDetail.setPricingKey("DEFAULT");
        priceDetail.setPriceSuffix(priceSuffix);
        priceDetail.setTaxesAndFeesText(taxAndFeeText);


        //TODO - FOR META
        if (metaInfo) {
            Map<String, String> metaMap = new HashMap<>();
            metaMap.put(SERVICE_CHARGE_KEY, String.valueOf(priceDetailOrch.getTaxBreakUp().getHotelServiceCharge()));
            metaMap.put(HOTEL_TAX_KEY, String.valueOf(priceDetailOrch.getTaxBreakUp().getHotelTax()));
            metaMap.put(SERVICE_FEES_KEY, String.valueOf(priceDetailOrch.getTaxBreakUp().getServiceFee()));
            //metaMap.put(AFFILIATE_FEES_KEY, String.valueOf(priceDetailOrch.getAffiliateFee()));
            //metaMap.put(WALLET_KEY, String.valueOf(priceDetailOrch.getWallet()));

            //TODO -
            //metaMap.put(MMT_DISCOUNT_KEY, String.valueOf(priceDetailOrch.getMMTDiscount()));
            //metaMap.put(BLACK_DISCOUNT_KEY, String.valueOf(priceDetailOrch.getBlackDiscount()));

            metaMap.put(CDF_DISCOUNT_KEY, String.valueOf(priceDetailOrch.getCouponDiscount()));

            if (priceDetailOrch.getApplicableCoupons() != null) {
                List<PriceCouponInfo> list = priceDetailOrch.getApplicableCoupons()
                        .stream()
                        .filter(entry -> entry.getDiscount() > 0.0d)
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(list)) {
                    for (PriceCouponInfo couponInfo : list) {
                        if (!couponInfo.getType().equalsIgnoreCase(PromotionalOfferType.MIXED.getName())) {
                            metaMap.put(couponInfo.getCouponCode(), String.valueOf(couponInfo.getDiscount()));
                        }
                    }
                }
            }
            metaMap.put(LOWEST_RATE_SEGMENT_KEY, lowestRateSegmentId);
            priceDetail.setMetaInfo(metaMap);
        }

        return priceDetail;
    }


    private Coupon buildCoupon(PriceDetail priceDetail) {
        List<PriceCouponInfo> couponInfoList = priceDetail.getApplicableCoupons();
        PriceCouponInfo couponInfo = CollectionUtils.isNotEmpty(couponInfoList) ? couponInfoList.get(0) : null;
        Coupon coupon = null;
        if (couponInfo == null && priceDetail.getCouponDiscount() > 0.0d) {
            coupon = new Coupon();
            coupon.setCode(priceDetail.getCouponCode());
            coupon.setCouponAmount(priceDetail.getCouponDiscount());
            //TODO: Fix - coupon.setPromoIcon(StringUtils.isNotEmpty(couponInfo.getPromoIconLink()) ? couponInfo.getPromoIconLink() : genericBankIcon);
//            coupon.setPromoIcon(genericBankIcon);
            PriceCouponInfo bestCoupon = getBestCoupon(priceDetail.getApplicableCoupons(), priceDetail.getCouponCode());
            if (bestCoupon != null) {
                coupon.setDescription(bestCoupon.getDescription());
                coupon.setSpecialPromo(bestCoupon.isSpecialPromoCoupon());
                coupon.setType(bestCoupon.getType());

            }
        } else if (couponInfo != null) {
            coupon = getCoupon(couponInfo);
        }
        return coupon;
    }


    private PriceCouponInfo getBestCoupon(List<PriceCouponInfo> applicableCoupons, String couponCode) {
        return CollectionUtils.isNotEmpty(applicableCoupons) ? applicableCoupons.stream().filter(coupon -> coupon.getCouponCode().equalsIgnoreCase(couponCode)).findFirst().orElse(null) : null;
    }

    private Coupon getCoupon(PriceCouponInfo couponInfo) {
        Coupon coupon = new Coupon();
        coupon.setDescription(couponInfo.getDescription());
        coupon.setCode(couponInfo.getCouponCode());
        coupon.setType(couponInfo.getType());
        coupon.setCouponAmount(couponInfo.getDiscount());
        coupon.setSpecialPromo(couponInfo.isSpecialPromoCoupon());
        //TODO: Fix - coupon.setPromoIcon(StringUtils.isNotEmpty(couponInfo.getPromoIconLink()) ? couponInfo.getPromoIconLink() : genericBankIcon);
//        coupon.setPromoIcon(genericBankIcon);
        return coupon;
    }

    private SoldOutInfoCG buildSoldOutInfo(SoldOutInfo soldOutInfo) {
        if (soldOutInfo == null)
            return null;
        SoldOutInfoCG soldOutInfoCG = new SoldOutInfoCG();
        soldOutInfoCG.setSoldOutText(soldOutInfo.getSoldOutText());
        soldOutInfoCG.setSoldOutSubText(soldOutInfo.getSoldOutSubText());
        soldOutInfoCG.setSoldOutReason(soldOutInfo.getSoldOutReason());
        soldOutInfoCG.setSoldOutType(soldOutInfo.getType());
        return soldOutInfoCG;
    }

    private List<String> getHydraSegments(CommonModifierResponse commonModifierResponse) {
        List<String> segments = null;
        if (commonModifierResponse != null && commonModifierResponse.getHydraResponse() != null && CollectionUtils.isNotEmpty(commonModifierResponse.getHydraResponse().getHydraMatchedSegment())) {
            segments = new ArrayList<>(commonModifierResponse.getHydraResponse().getHydraMatchedSegment());
        }
        return segments;
    }

    private Map<String, String> buildExpData(CommonModifierResponse commonModifierResponse) {
        return commonModifierResponse != null ? commonModifierResponse.getExpDataMap() : new HashMap<>();
    }

    private SortCriteria buildSortCriteria(com.gommt.hotels.orchestrator.model.objects.SortCriteria criteria) {
        return criteria == null ? null : new SortCriteria(criteria.getField(), criteria.getOrder());
    }

    protected String prepareBasicSharingUrl(String roomStayParam, String checkin, String checkout, String cityName, String cityCode,
                                          String countryCode, String rawDeepLink, String locationType, String currency, String siteDomain,
                                          String pageContext, String funnelSource, String parentLocationId, String parentLocationType, boolean checkAvailability) {

        checkin = dateUtil.getDateFormatted(checkin, DateUtil.YYYY_MM_DD, MMDDYYYY);
        checkout = dateUtil.getDateFormatted(checkout, DateUtil.YYYY_MM_DD, MMDDYYYY);

        String deepLink = MessageFormat.format(rawDeepLink, checkin, checkout, cityCode, countryCode, roomStayParam, checkAvailability, currency);


        if (cityName != null) {
            deepLink = deepLink + Constants.AND_SEPARATOR + Constants.SEARCHTEXT_URL_PARAM + Constants.PR_SEPARATOR + getEncodedUrl(cityName);
        }


        deepLink = deepLink + Constants.AND_SEPARATOR + Constants.LOCUSID_URL_PARAM + Constants.PR_SEPARATOR + cityCode;
        deepLink = deepLink + Constants.AND_SEPARATOR + Constants.LOCUSTYPE_URL_PARAM + Constants.PR_SEPARATOR + locationType;


        if (StringUtils.isNotEmpty(siteDomain)) {
            deepLink = deepLink + Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + siteDomain.toLowerCase();
        }
        if (Constants.STAYCATION.equalsIgnoreCase(pageContext))
            deepLink += Constants.AND_SEPARATOR + Constants.STAYCATION_FILTER_URL_PARAM + Constants.PR_SEPARATOR + "true";

        if (StringUtils.isNotBlank(funnelSource)) {
            deepLink += getQueryParameter(FUNNEL_NAME, funnelSource);
        }
        if (StringUtils.isNotEmpty(parentLocationId) && StringUtils.isNotEmpty(parentLocationType)) {
            deepLink = deepLink + Constants.AND_SEPARATOR + PARENT_LOCATION_ID + Constants.PR_SEPARATOR + parentLocationId;
            deepLink = deepLink + Constants.AND_SEPARATOR + PARENT_LOCATION_type + Constants.PR_SEPARATOR + parentLocationType;
        }

        return deepLink;
    }


    // Deeplink added as method param to make method dynamic
    public String prepareListingSharingUrl(SearchHotelsRequest searchHotelsRequest, String deeplink, boolean appendFilter, boolean checkAvailability, LocationDetails locationDetails) {

        // Handle potential nulls in search criteria and room stay candidates
        SearchHotelsCriteria searchCriteria = searchHotelsRequest.getSearchCriteria();
        String roomStayParam = "";
        if (searchCriteria != null && searchCriteria.getRoomStayCandidates() != null) {
//            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(searchCriteria.getRoomStayCandidates());
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchCriteria.getRoomStayCandidates());
        }

        // Prepare basic sharing URL
        String deepLink = prepareBasicSharingUrl(
                roomStayParam,
                searchCriteria != null ? searchCriteria.getCheckIn() : "",
                searchCriteria != null ? searchCriteria.getCheckOut() : "",
                searchCriteria != null && searchCriteria.getCityName()!=null ? searchCriteria.getCityName() : locationDetails != null ? locationDetails.getCityName() : "",
                searchCriteria != null && searchCriteria.getLocationId() != null? searchCriteria.getLocationId() : locationDetails != null ? locationDetails.getId() : "",
                searchCriteria != null && searchCriteria.getCountryCode() != null? searchCriteria.getCountryCode() : locationDetails != null ? locationDetails.getCountryId() : "",
                deeplink,
                searchCriteria != null &&  searchCriteria.getLocationType() != null ? searchCriteria.getLocationType() : locationDetails != null ? locationDetails.getType() : "",
                searchCriteria != null ? searchCriteria.getCurrency() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getSiteDomain() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getPageContext() : "",
                searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getFunnelSource() : "",
                searchCriteria != null ? searchCriteria.getParentLocationId() : "",
                searchCriteria != null ? searchCriteria.getParentLocationType() : "",
                checkAvailability
        );

        // Append filter data to the deepLink if needed
        if (appendFilter && CollectionUtils.isNotEmpty(searchHotelsRequest.getFilterCriteria())) {
            deepLink = appendFiltersDataToDeepLink(searchHotelsRequest.getFilterCriteria(), deepLink);
        }

        // Return the final deepLink
        return deepLink;
    }


    /*private String appendMmAreaTag(Tags areaObj) {
        // this function is to create the value that is to be used in mmAreaTag url parameter
        return new StringBuilder(areaObj.getTagDescription().replaceAll("\\s", "%20")).append(PIPE_UNICODE).append(areaObj.getTagAreaId()).toString();
    }


    private String appendMmPoiTag(LatLngObject poiObj) {
        // this function is to create the value that is to be used in mmPoiTag url parameter
        // LPOI should be appended to the deepLink in case of GPOI's case and POI in case of normal location poi which is identified by the presence of poiId node
        String mmPoiTagValue = new StringBuilder(poiObj.getPoiId() != null ? "POI" : "LPOI")
                .append(PIPE_UNICODE)
                .append(poiObj.getName().replaceAll("\\s", SPACE_UNICODE))
                .append(PIPE_UNICODE)
                .append(poiObj.getPoiId() != null ? poiObj.getPoiId() : poiObj.getPlaceId()).toString();

        if (poiObj.getLatitude() > 0.0 && poiObj.getLongitude() > 0.0) {
            mmPoiTagValue = new StringBuilder(mmPoiTagValue)
                    .append(PIPE_UNICODE)
                    .append(poiObj.getLatitude())
                    .append(PIPE_UNICODE)
                    .append(poiObj.getLongitude()).toString();
        }
        return mmPoiTagValue;
    }*/

    public String buildRoomStayCandidateFromSearchWrapper(List<com.mmt.hotels.clientgateway.request.RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }
        for (com.mmt.hotels.clientgateway.request.RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            int adultCount = roomStayCandidate.getAdultCount();
            int childCount = roomStayCandidate.getChildAges() != null ? roomStayCandidate.getChildAges().size() : 0;
            builder.append(adultCount);
            builder.append(Constants.RSQ_SPLITTER);
            builder.append(childCount);
            builder.append(Constants.RSQ_SPLITTER);
            if (CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges())) {
                for (int age : roomStayCandidate.getChildAges()) {
                    if (age >= 0 && age <= 12) {
                        builder.append(age);
                        builder.append(Constants.RSQ_SPLITTER);
                    }
                }
            }
        }
        return builder.toString();
    }


    public String buildRoomStayCandidateFromSearchWrapperS(@Nullable List<RoomStayCandidate> roomStayCandidates) {
        StringBuilder builder = new StringBuilder();
        if (CollectionUtils.isEmpty(roomStayCandidates)) {
            return builder.toString();
        }

        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            for (GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                if (guestCount == null)
                    continue;
                int adultCount = Integer.parseInt(guestCount.getCount());
                int childCount = 0;
                if (CollectionUtils.isNotEmpty(guestCount.getAges()))
                    childCount = guestCount.getAges().size();
                builder.append(adultCount);
                builder.append(Constants.RSQ_SPLITTER);
                builder.append(childCount);
                builder.append(Constants.RSQ_SPLITTER);
                if (CollectionUtils.isNotEmpty(guestCount.getAges())) {
                    for (int age : guestCount.getAges()) {
                        if (age >= 0 && age <= 12) {
                            builder.append(age);
                            builder.append(Constants.RSQ_SPLITTER);
                        }
                    }
                }
            }
        }

        String redisPrefixForRoomStayCandidate = builder.toString();
        return redisPrefixForRoomStayCandidate;
    }


    public static String getEncodedUrl(String url) {
        String encodedString = "";
        try {
            if (StringUtils.isNotBlank(url))
                encodedString = URLEncoder.encode(url, StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
        } catch (Exception e) {
            LOGGER.warn("Error while encoding url: {}", url);
        }
        return encodedString;
    }

    protected String getAppDeeplinkCrossSell(SearchHotelsRequest searchHotelsRequest) {
        String deepLink = listingApplDeeplink == null ? "" : listingApplDeeplink;
        if (isScionRequest(searchHotelsRequest) && isDeskTopRequest(searchHotelsRequest)) {
            deepLink = listinglDeeplink;
            if (searchHotelsRequest.getSearchCriteria().getUserGlobalInfo() != null && "GLOBAL".equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getUserGlobalInfo().getEntityName())) {
                deepLink = listinglDeeplinkGlobal;
                if (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getSiteDomain() != null && REGION_SA.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getSiteDomain())) {
                    deepLink = deepLink.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
                }
            }
        }
        return deepLink;
    }

    protected String getDetailDeeplink(SearchHotelsRequest searchHotelsRequest) {
        String deepLink = hotelLevelAppDeepLink == null ? "" : hotelLevelAppDeepLink;
        if (isScionRequest(searchHotelsRequest) && isDeskTopRequest(searchHotelsRequest)) {
            deepLink = basicDetailDeeplink;
            if (searchHotelsRequest.getSearchCriteria().getUserGlobalInfo() != null && "GLOBAL".equalsIgnoreCase(searchHotelsRequest.getSearchCriteria().getUserGlobalInfo().getEntityName())) {
                deepLink = basicDetailDeeplinkGlobal;
                if (searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getSiteDomain() != null && REGION_SA.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getSiteDomain())) {
                    deepLink = deepLink.replace(WWW_SUBDOMAIN, SA_SUBDOMAIN);
                }
            }
        }
        return deepLink;
    }


    public String buildHotelLevelAppDeepLink(SearchHotelsRequest searchHotelsRequest, Hotel hotelEntity, String rscValue, List<RoomStayCandidate> distributedRoomStayCandidateList, HotelDetails hotelDetails, LocationDetails locusData, String url) {
        try{
        // Initialize detail URL and handle potential nulls
            if (hotelEntity == null && hotelDetails !=null) {
                hotelEntity = new Hotel();
                LocationDetails locationDetails = hotelDetails.getLocation();
                if(locationDetails != null) {
                    LocationDetail locationDetail = new LocationDetail(locationDetails.getId(), locationDetails.getCityName(), locationDetails.getType(), locationDetails.getCountryId(), locationDetails.getCountryName());
                    hotelEntity.setId(hotelDetails.getId());
                    hotelEntity.setLocationDetail(locationDetail);
                    hotelEntity.setMaskedPropertyName(hotelDetails.isMaskedPropertyName());
                }
            }

        // Safely extract the room stay parameters from the search request
        SearchHotelsCriteria searchCriteria = searchHotelsRequest != null && searchHotelsRequest.getSearchCriteria() != null
                ? searchHotelsRequest.getSearchCriteria() : new SearchHotelsCriteria();

        String roomStayParam = "";
        if (CollectionUtils.isNotEmpty(distributedRoomStayCandidateList)) {
            roomStayParam = buildRoomStayCandidateFromSearchWrapperS(distributedRoomStayCandidateList);
        } else {
            roomStayParam = buildRoomStayCandidateFromSearchWrapper(searchCriteria.getRoomStayCandidates());
        }
        // Handle check-in and check-out dates; use empty strings if unavailable
        String checkin = searchCriteria.getCheckIn() != null
                ? dateUtil.getDateFormatted(searchCriteria.getCheckIn(), DateUtil.YYYY_MM_DD, MMDDYYYY)
                : "";
        String checkout = searchCriteria.getCheckOut() != null
                ? dateUtil.getDateFormatted(searchCriteria.getCheckOut(), DateUtil.YYYY_MM_DD, MMDDYYYY)
                : "";

        // Default values for missing parameters
        String checkAvailability = isScionRequest(searchHotelsRequest)?"false":"true";
        String cityCode = searchCriteria.getCityCode() == null ? searchCriteria.getLocationId() : searchCriteria.getCityCode();
            if(StringUtils.isEmpty(cityCode) && locusData != null && StringUtils.isNotEmpty(locusData.getId())){
                cityCode = locusData.getId();
            }

        // Determine country code, fallback on search criteria country code if hotel location is missing
        String countryCode = hotelEntity != null && hotelEntity.getLocationDetail() != null
                && StringUtils.isNotEmpty(hotelEntity.getLocationDetail().getCountryId())
                ? hotelEntity.getLocationDetail().getCountryId()
                : searchCriteria.getCountryCode() == null ? "" : searchCriteria.getCountryCode();

        boolean isCorporate = searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
                && Constants.CORP_ID_CONTEXT.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getIdContext());

        // Format the initial partial deep link
        String partialDeepLink = MessageFormat.format(url, "{0}",
                checkin,
                checkout,
                countryCode, cityCode, roomStayParam, searchCriteria.getCurrency() == null ? "" : searchCriteria.getCurrency(),
                checkAvailability);

        // Append location ID and location type if available
        if (StringUtils.isNotBlank(searchCriteria.getLocationId()) && StringUtils.isNotBlank(searchCriteria.getLocationType())) {
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_ID + Constants.PR_SEPARATOR + searchCriteria.getLocationId();
            partialDeepLink += Constants.AND_SEPARATOR + LOCUS_LOCATION_TYPE + Constants.PR_SEPARATOR + searchCriteria.getLocationType();
        }

            if(StringUtils.isBlank(searchCriteria.getLocationId()) && locusData != null && StringUtils.isNotEmpty(locusData.getId())) {
                partialDeepLink += Constants.AND_SEPARATOR + LOCUS_ID + Constants.PR_SEPARATOR + locusData.getId();
                partialDeepLink += Constants.AND_SEPARATOR + LOCUS_LOCATION_TYPE + Constants.PR_SEPARATOR + locusData.getType();
            }

        // Format and finalize the deep link with the hotel ID
        String completeDeepLink = MessageFormat.format(partialDeepLink, hotelEntity != null ? hotelEntity.getId() : "");

        // Append filter criteria if available
        if (searchHotelsRequest != null && searchHotelsRequest.getFilterCriteria() != null) {
            completeDeepLink = appendFiltersDataToDeepLink(searchHotelsRequest.getFilterCriteria(), completeDeepLink);
        }

        // Append region from MDC if available, fallback to default region
        String region = MDC.get(MDCHelper.MDCKeys.REGION.getStringValue());
        if (StringUtils.isNotEmpty(region)) {
            completeDeepLink += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + region.toLowerCase();
        } else {
            completeDeepLink += Constants.AND_SEPARATOR + REGION_URL_PARAM + Constants.PR_SEPARATOR + Constants.WALLET_REGION_IND;
        }

        // Append funnel source if available
        if (searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null
                && StringUtils.isNotBlank(searchHotelsRequest.getRequestDetails().getFunnelSource())) {
            completeDeepLink += getQueryParameter(FUNNEL_NAME, searchHotelsRequest.getRequestDetails().getFunnelSource());
        }

        // Append RSC value if available
        if (StringUtils.isNotEmpty(rscValue)) {
            completeDeepLink += getQueryParameter(RSC, rscValue);
        }

        if (isScionRequest(searchHotelsRequest) && searchHotelsRequest.getRequestDetails() != null && searchHotelsRequest.getRequestDetails().getFunnelSource() != null
                    && FUNNEL_SOURCE_SHORTSTAYS.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getFunnelSource())&& StringUtils.isNotBlank(hotelEntity.getLocationDetail().getId())) {
            completeDeepLink = completeDeepLink + Constants.AND_SEPARATOR + Constants.SEARCHTEXT_URL_PARAM + Constants.PR_SEPARATOR + hotelEntity.getLocationDetail().getId();
        }

        if(hotelEntity!=null){
            completeDeepLink += getQueryParameter(MPN,hotelEntity.isMaskedPropertyName());
        }

        return completeDeepLink;
        }catch (Exception e) {
            LOGGER.error("Error while building hotel level app deep link: ", e.getMessage());
            return "";
        }
    }

    public String getQueryParameter(String queryParam, boolean value){
        return Constants.AND_SEPARATOR + queryParam + Constants.PR_SEPARATOR + value;
    }

    public String getQueryParameter(String queryParam, String value) {
        return Constants.AND_SEPARATOR + queryParam + Constants.PR_SEPARATOR + value;
    }


    public String appendFiltersDataToDeepLink(List<Filter> filterCriteria, String deepLink) {
        if (CollectionUtils.isEmpty(filterCriteria)) {
            return deepLink; // Return the original deepLink if no filterCriteria is present
        }

        StringBuilder filters = new StringBuilder();

        for (Filter filter : filterCriteria) {
            if (filter.getFilterGroup() == null) {
                continue; // Skip filters with no FilterGroup
            }

            // Append the filter group name and the pipe splitter
            filters.append(filter.getFilterGroup().name()).append(Constants.PIPE_SPLITTER);

            // Check if the filter group is related to price
            if (filter.getFilterGroup().name().equals(FilterGroup.HOTEL_PRICE.name())
                    || filter.getFilterGroup().name().equals(FilterGroup.HOTEL_PRICE_BUCKET.name())) {

                // Initialize min and max values for price filters
                int minValue = Integer.MAX_VALUE;
                int maxValue = Integer.MIN_VALUE;

                // Update min and max values if a filter range is present
                if (filter.getFilterRange() != null) {
                    minValue = filter.getFilterRange().getMinValue();
                    maxValue = filter.getFilterRange().getMaxValue();
                }

                // If valid min and max values are found, append them
                if (minValue != Integer.MAX_VALUE && maxValue != Integer.MIN_VALUE) {
                    filters.append(minValue)
                            .append(Constants.HYPHEN)
                            .append(maxValue)
                            .append(Constants.COMMA);
                }
            } else {
                // Append the filter value if it exists
                if (StringUtils.isNotEmpty(filter.getFilterValue())) {
                    filters.append(filter.getFilterValue()).append(Constants.COMMA);
                }
            }

            // Remove the last comma and append the group splitter
            if (filters.length() > 0 && filters.charAt(filters.length() - 1) == Constants.COMMA.charAt(0)) {
                filters.deleteCharAt(filters.length() - 1); // Remove trailing comma
            }
            filters.append(Constants.DEEPLINK_FILTER_GROUP_SPLITTER); // Append filter group splitter
        }

        // Remove the last filter group splitter if present
        if (filters.length() > 0 && filters.charAt(filters.length() - 1) == Constants.DEEPLINK_FILTER_GROUP_SPLITTER.charAt(0)) {
            filters.deleteCharAt(filters.length() - 1); // Remove trailing group splitter
        }

        // Append filters to the deep link if available
        if (StringUtils.isNotBlank(filters.toString())) {
            return deepLink + Constants.AND_SEPARATOR + Constants.DEEPLINK_FILTER_DATA + Constants.PR_SEPARATOR
                    + getEncodedUrl(filters.toString());
        }

        // Return the original deepLink if no valid filters were appended
        return deepLink;
    }

    public boolean isScionRequest(SearchHotelsRequest searchHotelsRequest) {
        return searchHotelsRequest != null && searchHotelsRequest.getRequestDetails() != null &&
                (Constants.REQUESTOR_SCION.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getRequestor()) || TRAFFIC_SOURCE_CROSSSELL.equalsIgnoreCase(searchHotelsRequest.getRequestDetails().getRequestor()));
    }

    public boolean isDeskTopRequest(SearchHotelsRequest searchHotelsRequest) {
        return searchHotelsRequest != null && searchHotelsRequest.getDeviceDetails() != null &&
                (Constants.DEVICE_OS_DESKTOP.equalsIgnoreCase(searchHotelsRequest.getDeviceDetails().getDeviceType()) || DEVICE_OS_PWA.equalsIgnoreCase(searchHotelsRequest.getDeviceDetails().getDeviceType()));
    }
}
