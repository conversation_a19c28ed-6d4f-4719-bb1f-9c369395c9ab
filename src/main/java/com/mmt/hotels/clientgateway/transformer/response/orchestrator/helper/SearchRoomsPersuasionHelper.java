package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.pricing.RatePlan;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.response.PersuasionResponse;
import com.mmt.hotels.clientgateway.response.Style;
import com.mmt.hotels.clientgateway.response.rooms.SelectRoomRatePlan;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Helper class for building persuasions for OrchV2 SearchRooms response
 * Currently handles rate plan level persuasions
 * TODO: Add search rooms response level persuasions when legacy dependencies are resolved
 */
@Component
public class SearchRoomsPersuasionHelper {

    @Autowired
    private PolyglotService polyglotService;

    /**
     * Build rate plan specific persuasions for OrchV2 rate plan
     * Migrated from legacy SearchRoomsResponseTransformer.getRatePlanPersuasion method
     */
    public List<PersuasionResponse> getRatePlanPersuasion(SelectRoomRatePlan ratePlan, RatePlan ratePlanOrchV2,
                                                          String funnelSource, CommonModifierResponse commonModifierResponse, boolean isLuxeHotel) {

        List<PersuasionResponse> persuasions = null;

        // TODO: MyPartner exclusive rate persuasion - needs MYPARTNER_EXCLUSIVE_RATE_TEXT constant
        // TODO: Confirmation policy persuasion - needs getConfirmationPolicy() method in OrchV2 RatePlan

        // Staycation deal persuasion
        if (ratePlanOrchV2 != null && ratePlanOrchV2.getRatePlanFlags() != null && ratePlanOrchV2.getRatePlanFlags().isPackageRatePlan() &&
                ratePlanOrchV2.getRatePlanFlags().isStaycationDeal() && "GETAWAY".equalsIgnoreCase(funnelSource)) {
            persuasions = new ArrayList<>();
            PersuasionResponse persuasion = new PersuasionResponse();
            persuasion.setPersuasionText(polyglotService.getTranslatedData("GETAWAY_DEAL_PERSUASION_TEXT"));
            persuasion.setPlaceholderId("rightBottom");
            persuasion.setId("STAYCATION");
            persuasion.setTemplate("TEXT_WITH_BG_IMAGE");
            persuasion.setStyle(new Style());
            persuasion.getStyle().setTextColor("#4a4a4a");
            persuasion.getStyle().setFontSize("SMALL");
            persuasion.getStyle().setBgUrl("https://promos.makemytrip.com/Hotels_product/package/tag-bkg.png");
            persuasions.add(persuasion);
        }

        return persuasions;
    }

    //TODO - Add for Mypartner
    public Map<String, PersuasionResponse> buildRatePlanPersuasionsMap(RatePlan ratePlan, CommonModifierResponse commonModifierResponse) {
        Map<String, PersuasionResponse> persuasionResponseMap = new HashMap<>();
        return persuasionResponseMap;
    }
}