package com.mmt.hotels.clientgateway.transformer.response.orchestrator;

import com.fasterxml.jackson.databind.JsonNode;
import com.gommt.hotels.orchestrator.detail.enums.OTA;
import com.gommt.hotels.orchestrator.detail.model.response.HotelStaticContentResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.AmenitiesInfo;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenityGroup;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity;
import com.gi.hotels.model.response.staticdata.Categorized;
import com.gommt.hotels.orchestrator.detail.model.response.content.amenity.AmenitySubAttribute;
import com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.ArrangementInfo;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData;
import com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.PersuasionValue;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.SortingCriteria;
import com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary;
import com.gommt.hotels.orchestrator.model.response.listing.HotelDetails;
import com.gommt.hotels.orchestrator.model.response.listing.PersonalizedSectionDetails;
import com.mmt.hotels.clientgateway.constants.ConstantsTranslation;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.ReviewSummary;
import com.mmt.hotels.clientgateway.response.ReviewObject;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.CardDataResponseHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.ChatBotPersuasionHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.HotelResultHelper;
import com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper.MediaResponseHelper;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.model.request.flyfish.ReviewSummaryListItem;
import com.mmt.hotels.model.request.upsell.BannerInfo;
import com.mmt.hotels.model.response.flyfish.ExtRating;
import com.mmt.hotels.model.response.flyfish.ReviewSortingCriterionListDTO;
import com.gommt.hotels.orchestrator.detail.model.response.content.RulesAndPolicies;

import com.gommt.hotels.orchestrator.detail.model.response.content.PlacesResponse;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.Category;
import com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum;
import com.mmt.hotels.model.request.flyfish.*;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;

import com.mmt.hotels.clientgateway.response.*;
import com.mmt.hotels.clientgateway.response.rooms.RoomDetails;
import com.mmt.hotels.clientgateway.response.searchHotels.Hotel;
import com.mmt.hotels.clientgateway.response.staticdetail.*;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.model.request.flyfish.SeekTagDetailsDTO;
import com.mmt.hotels.model.request.flyfish.SeekTagTopicSummaryDTO;
import com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO;
import com.mmt.hotels.model.response.staticdata.*;
import com.mmt.hotels.pojo.response.detail.ImageAuthor;
import com.mmt.model.SleepingArrangement;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.Objects;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.enums.ExperimentKeys.LOCATION_SECTION_RATING;
import static java.lang.Math.min;




public abstract class OrchStaticDetailResponseTransformer extends HotelResultHelper {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrchStaticDetailResponseTransformer.class);
    private static final com.fasterxml.jackson.databind.ObjectMapper MAPPER = new com.fasterxml.jackson.databind.ObjectMapper();
    
    // Define missing constants locally until they're available in Constants class
    private static final Map<String, String> NATIONALITY_MAP = initNationalityMap();
    private static final int MIN_COUNTRY_WISE_REVIEW_COUNT = 3;
    private static final String COUNTRY_WISE_REVIEW_TEXT = "COUNTRY_WISE_REVIEW_TEXT";
    private static final String COUNTRY_WISE_REVIEW_CTA_TEXT = "COUNTRY_WISE_REVIEW_CTA_TEXT";
    private static final String COUNTRY_WISE_REVIEW_TEXT_COLOR = "#4A90E2";
    private static final String REVIEW_COUNT = "{REVIEW_COUNT}";
    private static final String NATIONALITY = "{NATIONALITY}";
    private static final String HOST_NO_RATING_TEXT = "HOST_NO_RATING_TEXT";
    private static final String CHATBOT_HOOKS_EXP = "chatbot_hooks_exp";
    private static final String amenitiesGiV2 = "amenitiesGiV2";
    
    private static Map<String, String> initNationalityMap() {
        Map<String, String> map = new HashMap<>();
        map.put("IN", "Indian");
        map.put("US", "American");
        map.put("UK", "British");
        map.put("AE", "Emirati");
        map.put("SG", "Singaporean");
        // Add more as needed
        return map;
    }

    @Autowired
    private MobConfigHelper mobConfigHelper;

    @Autowired
    private PolyglotService polyglotService;

    @Autowired
    private ReArchUtility utility;

    @Autowired
    private ChatBotPersuasionHelper chatBotPersuasionHelper;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;


    @Autowired
    CardDataResponseHelper cardDataResponseHelper;



    @Autowired
    private MediaResponseHelper mediaResponseHelper;

    @Autowired
    protected CommonResponseTransformer commonResponseTransformer;

    @Value("${hostImpression.title.tag.url}")
    private String hostImpressionTitleTagUrl;


    @Autowired
    private OrchSearchHotelsResponseTransformerSCION orchSearchHotelsResponseTransformer;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Value("${reposition.index.comparator}")
    protected int repositionIndex;

    @Value("${sponsored.hotel.icon.url}")
    private String sponsoredHotelIconUrl;

    @Value("${gia.ai.icon.url}")
    private String giaIconURl;

    /**
     * Maps fields from HotelStaticContentResponse (Orchestrator) to StaticDetailResponse (ClientGateway).
     * This is a utility for use in the new re-arch flow.
     * @param source The orchestrator response object (should be HotelStaticContentResponse)
     * @return The mapped StaticDetailResponse
     */
    public StaticDetailResponse convertStaticDetailResponse(StaticDetailRequest staticDetailRequest, HotelStaticContentResponse source, CommonModifierResponse commonModifierResponse) {
        if (source == null) return null;
        
        try {
            StaticDetailResponse target = new StaticDetailResponse();
            
            // Extract common properties for logic - align with reference transformer  
            Map<String, String> expDataMap = utility.getExpDataMap(staticDetailRequest.getExpData());
            boolean isInternational = isInternationalProperty(source, staticDetailRequest.getSearchCriteria());
            staticDetailRequest.setExpDataMap(expDataMap);

            String countryCode = "";
            if (staticDetailRequest != null && staticDetailRequest.getSearchCriteria() != null && staticDetailRequest.getSearchCriteria().getCountryCode() != null) {
                countryCode = staticDetailRequest.getSearchCriteria().getCountryCode();
            }

            boolean isLiteResponse = staticDetailRequest != null && staticDetailRequest.getFeatureFlags() != null && staticDetailRequest.getFeatureFlags().isLiteResponse();
            boolean isLuxe = source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null && source.getHotelMetaData().getPropertyDetails().getCategories() != null && source.getHotelMetaData().getPropertyDetails().getCategories().contains(LUXURY_HOTELS);
            boolean isChatBotEnable = source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyFlags() != null &&
                    source.getHotelMetaData().getPropertyFlags().isChatbotEnabled() &&
                    MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(CHATBOT_HOOKS_EXP) && TRUE.equalsIgnoreCase(expDataMap.get(CHATBOT_HOOKS_EXP));
            boolean isImageExpEnable = MapUtils.isNotEmpty(expDataMap) && expDataMap.containsKey(IMAGES_EXP_ENABLE) && TRUE.equalsIgnoreCase(expDataMap.get(IMAGES_EXP_ENABLE));

            String listingType = "";
            if ( source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null && StringUtils.isNotBlank( source.getHotelMetaData().getPropertyDetails().getListingType())) {
                listingType = source.getHotelMetaData().getPropertyDetails().getListingType();
            }

            // PHASE 1: Core Business Logic Fields
            
            // Direct orchestrator field mappings
            target.setUuids(source.getPendingRequestsUuids());
            target.setCompletedRequests(source.getCompletedRequestsUuids());
            target.setWeaverResponse(source.getWeaverResponse());
            
            // Add support for experiment data mapping - align with reference pattern
            if (commonModifierResponse != null) {
                target.setExpData(commonModifierResponse.getExpDataMap());
                target.setVariantKey(commonModifierResponse.getVariantKey());
            }
            
            // Set experiment data if available - align with reference pattern
            target.setExpVariantKeys(StringUtils.isNotBlank(staticDetailRequest.getExpVariantKeys()) ? staticDetailRequest.getExpVariantKeys() : null);

            // UGC and review summaries mapping - align with reference transformer logic
            if (source.getTravellerReviewSummary() != null) {
                target.setUgcSummary(buildUgcReviewSummary(source.getTravellerReviewSummary(), commonModifierResponse, countryCode));
            }
            if(target.getUgcSummary() != null && target.getUgcSummary().getData() != null && source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null) {
                target.getUgcSummary().getData().setHotelId(source.getHotelMetaData().getPropertyDetails().getId());
            }

            // Implement amenitiesGI based on UGCV2 experiment logic
            boolean isUGCV2ExperimentEnabled = utility.isExperimentOn(expDataMap, UGCV2) || 
                                             utility.isExperimentOn(expDataMap, amenitiesGiV2);
            
            if (isUGCV2ExperimentEnabled) {
                // Set amenitiesGI for non-UGCV2 flow using HotelResultMapper delegation 
                if (source.getHotelMetaData() != null && source.getHotelMetaData().getAmenitiesInfo() != null
                    && CollectionUtils.isNotEmpty(source.getHotelMetaData().getAmenitiesInfo().getAmenities())) {
                    target.setAmenitiesGI(convertOrchestratorAmenitiesGI(source.getHotelMetaData().getAmenitiesInfo()));
                }
                if(expDataMap.containsKey(amenitiesGiV2) && TRUE.equalsIgnoreCase(expDataMap.get(amenitiesGiV2)) && source.getHotelMetaData() != null && source.getHotelMetaData().getAmenitiesInfo() != null
                        && CollectionUtils.isNotEmpty(source.getHotelMetaData().getAmenitiesInfo().getAmenities())) {
                    //categorizedV2 node will contain the amenities data for GI
                    utility.updateAmenitiesGIRearch(target, source.getHotelMetaData().getAmenitiesInfo().getAmenities());
                }
            }
            // PHASE 2: Media & Visual Content Fields
            if (source.getMedia() != null && source.getHotelMetaData() != null) {
                target.setMediaV2(mediaResponseHelper.buildMedia(source.getMedia(), isChatBotEnable, isImageExpEnable, isLuxe, staticDetailRequest.getClient(), listingType));
            }

            // Map treelGalleryData from orchestrator media
            if (source != null && source.getMedia() != null && source.getMedia().getTreelsImages() != null) {
                target.setTreelGalleryData(mediaResponseHelper.mapTreelsImagesToTreelGalleryData(source.getMedia().getTreelsImages()));
            }

            // Enhanced lite response processing - align with reference transformer logic
            if(isLiteResponse && staticDetailRequest != null && CollectionUtils.isEmpty(staticDetailRequest.getUuids())){
                if(target != null && target.getMediaV2() != null) {
                    //set grid images for lite response
                    if(target.getMediaV2().getGrid() != null && CollectionUtils.isNotEmpty(target.getMediaV2().getGrid().getImages())) {
                        target.setHotelGridImages(target.getMediaV2().getGrid().getImages());
                    }

                    //set hotelImageListCount for lite response
                    if(target.getMediaV2().getHotel() != null && CollectionUtils.isNotEmpty(target.getMediaV2().getHotel().getTags())){
                        target.setHotelImageListCount(getMediaV2HotelMediaListCount(target.getMediaV2().getHotel().getTags()));
                    }

                    //set hotelImageByTravellerCount and hotelImageByTravellerL1 for lite response
                    if(target.getMediaV2().getTraveller() != null && CollectionUtils.isNotEmpty(target.getMediaV2().getTraveller().getTags())) {
                        List<LiteResponseTravellerImage> liteResponseTravellerImages = getMediaV2TravellerMediaList(target.getMediaV2().getTraveller().getTags());
                        if(CollectionUtils.isNotEmpty(liteResponseTravellerImages)){
                            target.setHotelImageByTravellerCount(liteResponseTravellerImages.size());
                            target.setHotelImageByTravellerL1(liteResponseTravellerImages.subList(0, min(liteResponseTravellerImages.size(), 4)));
                        }
                    }
                }
            }

            // PHASE 3: Hotel Details & Room Information

            // Map roomInfoMap
            if (source.getRoomInfoMap() != null) {
                Map<String, RoomDetails> roomDetailsMap = new HashMap<>();
                for (Map.Entry<String,   com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo> entry : source.getRoomInfoMap().entrySet()) {
                    roomDetailsMap.put(entry.getKey(), mapRoomInfoToRoomDetails(entry.getValue(), source.getMedia()));
                }
                target.setRoomInfoMap(roomDetailsMap);
            }

            // Map hotelDetails using HotelResultMapper - align with reference transformer pattern
            if (source.getHotelMetaData() != null) {
                // Extract required parameters following reference transformer pattern
                String context = DEFAULT; // Default context
                if ( source.getHotelMetaData().getPropertyDetails() != null && StringUtils.isNotEmpty(source.getHotelMetaData().getPropertyDetails().getContext())) {
                    context = source.getHotelMetaData().getPropertyDetails().getContext();
                }
                boolean isGroupBookingFunnel = utility.isGroupBookingFunnel(staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null);
                String funnel = staticDetailRequest.getRequestDetails() != null ? staticDetailRequest.getRequestDetails().getFunnelSource() : null;
                
                // Use HotelResultMapper for consistent hotel details mapping 
                target.setHotelDetails(getHotelResult(
                        staticDetailRequest,
                        source.getHotelMetaData(),
                        context,
                        expDataMap,
                        isGroupBookingFunnel,
                        funnel
                ));
            }

            if (source.getHotelMetaData() != null && source.getHotelMetaData().getHostingInfo() != null && source.getHotelMetaData().getHostingInfo().getStaffInfo() != null) {
                target.getHotelDetails().setStaffInfo(convertStaffInfo(source.getHotelMetaData().getHostingInfo().getStaffInfo()));
            }
            
            // Map policy messages and currency
            target.setPolicyMessages(mapPolicyMessages(source));
            target.setCurrency(mapCurrency(source, staticDetailRequest));
            
            // Map BHF persuasions from orchestrator HotelMetaData.bhfPersuasions
            target.setBhfPersuasions(mapBhfPersuasions(source));
            target.setDetailsTopBhf(mapDetailTopBhfPersuasion(source));
            target.setDetailsRrBfh(mapDetailRrBhfPersuasion(source));
            target.setDetailsBlockerBhf(mapDetailBlockerBhfPersuasion(source));
            
            // Map persuasion detail
            target.setPersuasionDetail(mapPersuasionDetail(source));

            // PHASE 6: Comparator & Places Integration

            String rscValue = utility.buildRscValue(staticDetailRequest.getSearchCriteria().getRoomStayCandidates());
            // Map comparator responses - align with reference transformer
            if (MapUtils.isNotEmpty(source.getComparatorResponse()) && null != staticDetailRequest.getRequiredApis() && staticDetailRequest.getRequiredApis().isComparatorV2Required()) {
                SearchHotelsRequest searchHotelsRequest = transformStaticDetailRequestToSearchHotelsRequest(staticDetailRequest);
                target.setHotelCompareResponse(buildHotelCompareResponseResponse(staticDetailRequest, searchHotelsRequest, source.getComparatorResponse(), expDataMap, isLiteResponse, commonModifierResponse, rscValue));
                if (source.getComparatorResponse().containsKey("CHAIN_HOTELS") && utility.isExperimentOn(expDataMap, "CRI")) {
                    target.setChainCompareResponse(buildChainComparatorResponse(staticDetailRequest, searchHotelsRequest, source.getComparatorResponse().get("CHAIN_HOTELS"), expDataMap, isLiteResponse, commonModifierResponse));
                }
            }
            if (source.getPlacesResponse() != null) {
                target.setPlacesResponse(modifyPlacesResponse(source.getPlacesResponse()));
                target.setPlacesResponseV2(mapToPlacesResponseCG(source.getPlacesResponse(), expDataMap));
            }

            // Map personalization cards
            target.setDetailPersuasionCards(cardDataResponseHelper.buildListPersonalizationResponse(source.getPersonalizationCards()));


            target.setUuids(source.getPendingRequestsUuids() != null ? source.getPendingRequestsUuids() : Collections.EMPTY_SET);
            target.setCompletedRequests(source.getCompletedRequestsUuids());

            target.setExpVariantKeys(StringUtils.isNotBlank(staticDetailRequest.getExpVariantKeys()) ? staticDetailRequest.getExpVariantKeys() : null);

            target.setChatBotPersuasions(chatBotPersuasionHelper.mapChatBotPersuasions(source.getChatBotPersuasion()));


            
            // Map luxuryCards based on property categories
//            if (isLuxe && source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null) {
//                List<com.mmt.hotels.clientgateway.response.staticdetail.LuxuryCard> luxuryCards = new ArrayList<>();
//                // Create a basic luxury card based on property being luxury
//                com.mmt.hotels.clientgateway.response.staticdetail.LuxuryCard luxuryCard =
//                    new com.mmt.hotels.clientgateway.response.staticdetail.LuxuryCard();
//                luxuryCard.setHeading("Luxury Experience");
//                luxuryCard.setText("Premium luxury hotel experience");
//                luxuryCard.setImageUrl(source.getHotelMetaData().getPropertyDetails().getHotelIcon());
//                luxuryCards.add(luxuryCard);
//                target.setLuxuryCards(luxuryCards);
//            }
            
            // Map additional fields that exist in StaticDetailResponse
            if (source.getHotelMetaData() != null && source.getHotelMetaData().getPropertyDetails() != null) {
                // Set variant key for tracking
                target.setVariantKey(expDataMap != null ? expDataMap.getOrDefault("variant_key", "") : "");
                
                // Set policy messages if available
                if (source.getHotelMetaData().getRulesAndPolicies() != null) {
                    target.setPolicyMessages(mapPolicyMessages(source));
                }
                
                // Set luxury cards based on property type
                if (isLuxe) {
                    target.setLuxuryCards(new ArrayList<>()); // Initialize empty list for luxury cards
                }
                
                // Set images info
                if (source.getMedia() != null) {
                    target.setImages(mapLegacyImageInfo(source));
                }
                
                // Set amenities GI
                if (source.getHotelMetaData().getAmenitiesInfo() != null) {
                    target.setAmenitiesGI(convertOrchestratorAmenitiesGI(source.getHotelMetaData().getAmenitiesInfo()));
                }
            }

            return target;
            
        } catch (Exception e) {
            LOGGER.error("Error converting StaticDetailResponse: ", e);
            return null;
        }
    }

    public StaffInfo convertStaffInfo( com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo == null) return null;
        
        StaffInfo staffInfoCg = new StaffInfo();
        
        // Map basic fields
        staffInfoCg.setIsStarHost(staffInfo.getIsStarHost());
        staffInfoCg.setStarHostIconUrl(staffInfo.getStarHostIconUrl());
        staffInfoCg.setChatEnabled(staffInfo.getChatEnabled());
        staffInfoCg.setResponseTime(staffInfo.getResponseTime());
        staffInfoCg.setStarHostReasons(staffInfo.getStarHostReasons());
        
        // Map host Staff
        if (staffInfo.getHost() != null) {
            staffInfoCg.setHost(mapStaffToStaffCg(staffInfo.getHost()));
        }
        
        // Map caretaker Staff
        if (staffInfo.getCaretaker() != null) {
            staffInfoCg.setCaretaker(mapStaffToStaffCg(staffInfo.getCaretaker()));
        }
        
        // Map cook Staff
        if (staffInfo.getCook() != null) {
            staffInfoCg.setCook(mapStaffToStaffCg(staffInfo.getCook()));
        }
        
        return staffInfoCg;
    }
    
    /**
     * Maps Staff from orchestrator to client gateway Staff
     */
    private Staff mapStaffToStaffCg(
              com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Staff staff) {
        if (staff == null) return null;
        
        Staff staffCg = new Staff();
        
        // Map header
        staffCg.setHeader(staff.getHeader());
        
        // Map staff data list
        if (staff.getData() != null && !staff.getData().isEmpty()) {
            List<StaffData> staffDataList = new ArrayList<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staff.getData()) {
                staffDataList.add(mapStaffDataToStaffDataCg(staffData));
            }
            staffCg.setData(staffDataList);
        }
        
        return staffCg;
    }
    
    /**
     * Maps StaffData from orchestrator to client gateway StaffData
     */
    private StaffData mapStaffDataToStaffDataCg(
              com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData) {
        if (staffData == null) return null;
        
        StaffData staffDataCg = new StaffData();
        
        // Map all fields
        staffDataCg.setType(staffData.getType());
        staffDataCg.setHeading(staffData.getHeading());
        staffDataCg.setName(staffData.getName());
        staffDataCg.setGender(staffData.getGender());
        staffDataCg.setAge(staffData.getAge());
        staffDataCg.setProfilePicUrl(staffData.getProfilePicUrl());
        staffDataCg.setAbout(staffData.getAbout());
        
        // Map general info (SpecialisedIn list)
        if (staffData.getGeneralInfo() != null && !staffData.getGeneralInfo().isEmpty()) {
            List<SpecialisedIn> specialisedInList = new ArrayList<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.SpecialisedIn specialisedIn : staffData.getGeneralInfo()) {
                specialisedInList.add(mapSpecialisedInToSpecialisedInCg(specialisedIn));
            }
            staffDataCg.setGeneralInfo(specialisedInList);
        }
        
        // Map availability
        if (staffData.getAvailability() != null) {
            staffDataCg.setAvailability(mapAvailabilityToAvailabilityCg(staffData.getAvailability()));
        }
        
        // Map responsibilities
        if (staffData.getResponsibilities() != null) {
            staffDataCg.setResponsibilities(mapResponsibilitiesToResponsibilitiesCg(staffData.getResponsibilities()));
        }
        
        return staffDataCg;
    }
    
    /**
     * Maps SpecialisedIn from orchestrator to client gateway SpecialisedIn
     */
    private SpecialisedIn mapSpecialisedInToSpecialisedInCg(
              com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.SpecialisedIn specialisedIn) {
        if (specialisedIn == null) return null;
        
        SpecialisedIn specialisedInCg =
                new SpecialisedIn();
        
        // Map text and iconUrl
        specialisedInCg.setText(specialisedIn.getText());
        specialisedInCg.setIconUrl(specialisedIn.getIconUrl());
        
        return specialisedInCg;
    }
    
    /**
     * Maps Availability from orchestrator to client gateway Availability
     */
    private Availability mapAvailabilityToAvailabilityCg(
              com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Availability availability) {
        if (availability == null) return null;
        
        Availability availabilityCg =
                new Availability();
        
        // Map text and subText fields
        availabilityCg.setText(availability.getText());
        availabilityCg.setSubText(availability.getSubText());
        
        return availabilityCg;
    }
    
    /**
     * Maps Responsibilities from orchestrator to client gateway Responsibilities
     */
    private Responsibilities mapResponsibilitiesToResponsibilitiesCg(
              com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Responsibilities responsibilities) {
        if (responsibilities == null) return null;
        
        Responsibilities responsibilitiesCg =
                new Responsibilities();
        
        // Map text
        responsibilitiesCg.setText(responsibilities.getText());
        
        // Map specialisedIn list
        if (responsibilities.getSpecialisedIn() != null && !responsibilities.getSpecialisedIn().isEmpty()) {
            List<SpecialisedIn> specialisedInList = new ArrayList<>();
            for ( com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.SpecialisedIn specialisedIn : responsibilities.getSpecialisedIn()) {
                specialisedInList.add(mapSpecialisedInToSpecialisedInCg(specialisedIn));
            }
            responsibilitiesCg.setSpecialisedIn(specialisedInList);
        }
        
        return responsibilitiesCg;
    }

    public com.mmt.hotels.pojo.response.detail.PlacesResponse modifyPlacesResponse(PlacesResponse placesResponse) {
        com.mmt.hotels.pojo.response.detail.PlacesResponse placesResponseCg = new com.mmt.hotels.pojo.response.detail.PlacesResponse();
        if (placesResponse != null) {
            placesResponseCg.setCardType(placesResponse.getCardType());
            placesResponseCg.setCategories(mapCategories(placesResponse.getCategories()));
            placesResponseCg.setDirectionsToReach(mapDirectionDetails(placesResponse.getDirectionsToReach()));
            // Map location rating data if available
            placesResponseCg.setLocRatingData(mapRatingDataToUGCRatingData(placesResponse.getRatingData()));
        }
        return placesResponseCg;
    }

    private List<com.mmt.hotels.pojo.response.detail.placesapi.Category> mapCategories(List< com.gommt.hotels.orchestrator.detail.model.response.content.places.Category> sourceList) {
        if (sourceList == null) return null;
        List<com.mmt.hotels.pojo.response.detail.placesapi.Category> targetList = new ArrayList<>();
        for ( com.gommt.hotels.orchestrator.detail.model.response.content.places.Category src : sourceList) {
            com.mmt.hotels.pojo.response.detail.placesapi.Category tgt = new com.mmt.hotels.pojo.response.detail.placesapi.Category();
            tgt.setPriority(src.getPriority());
            tgt.setIconUrl(src.getIconUrl());
            tgt.setCategoryType(src.getCategoryType());
            targetList.add(tgt);
            tgt.setCategoryData(mapCategoryData(src.getCategoryData()));
        }
        return targetList;
    }

    private List<com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum> mapCategoryData(List< com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum> sourceList) {
        if (sourceList == null) return null;
        List<com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum> targetList = new ArrayList<>();
        for ( com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum src : sourceList) {
            com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum tgt = new com.mmt.hotels.pojo.response.detail.placesapi.CategoryDatum();
            tgt.setPlaceId(src.getPlaceId());
            tgt.setPlaceName(src.getPlaceName());
            tgt.setLocation(mapLocation(src.getLocation()));
            tgt.setCategory(src.getCategory());
            tgt.setDistance(src.getDistance());
            tgt.setDistanceUnit(src.getDistanceUnit());
            tgt.setSeoUrl(src.getSeoUrl());
            tgt.setVoyId(src.getVoyId());
            tgt.setCtaUrl(src.getCtaUrl());
            tgt.setAddress(src.getAddress());
            tgt.setTagLine(src.getTagLine());
            tgt.setPerformanceTags(src.getPerformanceTags());
            tgt.setImageList(mapPoiImageList(src.getImageList()));
            targetList.add(tgt);
        }
        return targetList;
    }

    // Helper to map Location
    private com.mmt.hotels.pojo.response.detail.placesapi.Location mapLocation( com.gommt.hotels.orchestrator.detail.model.response.content.places.Location src) {
        if (src == null) return null;
        com.mmt.hotels.pojo.response.detail.placesapi.Location tgt = new com.mmt.hotels.pojo.response.detail.placesapi.Location();
        tgt.setLat(src.getLat());
        tgt.setLon(src.getLon());
        tgt.setType(src.getType());
        return tgt;
    }

    // Helper to map PoiImage list
    private List<com.mmt.hotels.pojo.response.detail.PoiImage> mapPoiImageList(List< com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage> srcList) {
        if (srcList == null) return null;
        List<com.mmt.hotels.pojo.response.detail.PoiImage> tgtList = new ArrayList<>();
        for ( com.gommt.hotels.orchestrator.detail.model.response.content.places.PoiImage src : srcList) {
            com.mmt.hotels.pojo.response.detail.PoiImage tgt = new com.mmt.hotels.pojo.response.detail.PoiImage();
            tgt.setUrl(src.getUrl());
            tgt.setThumbnail(src.isThumbnail());
            if(src.getAuthor() != null) {
                ImageAuthor imageAuthor = new ImageAuthor();
                imageAuthor.setName(src.getAuthor().getName());
                imageAuthor.setLink(src.getAuthor().getLink());
                tgt.setAuthor(imageAuthor);
            }
            tgtList.add(tgt);
        }
        return tgtList;
    }

    private com.mmt.hotels.pojo.response.detail.placesapi.DirectionDetails mapDirectionDetails( com.gommt.hotels.orchestrator.detail.model.response.content.places.DirectionDetails src) {
        if (src == null) return null;
        com.mmt.hotels.pojo.response.detail.placesapi.DirectionDetails tgt = new com.mmt.hotels.pojo.response.detail.placesapi.DirectionDetails();
        tgt.setAddressDetail(src.getAddressDetail());
        tgt.setCtaText(src.getCtaText());
        return tgt;
    }

    public List<Hotel> getListingHotels(SearchHotelsRequest searchHotelsRequest,
                                        com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse comparatorResponse,
                                        CommonModifierResponse commonModifierResponse, Map<String, String> expDataMap, boolean isLiteResponse, boolean sponsoredHotel) {
        List<com.gommt.hotels.orchestrator.model.response.listing.HotelDetails> hotels = new ArrayList<>();
        if(comparatorResponse.getPersonalizedSections() != null && CollectionUtils.isNotEmpty(comparatorResponse.getPersonalizedSections().getHotels())) {
            hotels = comparatorResponse.getPersonalizedSections().getHotels();
        }

        List<Hotel> transformedHotels = searchHotelsFactory.getSearchHotelsResponseService(searchHotelsRequest.getClient()).buildPersonalizedHotels(
                hotels,
                expDataMap,
                searchHotelsRequest,
                comparatorResponse.getSectionTitle(), // section name
                commonModifierResponse, // commonModifierResponse
                null
        );
        if (isLiteResponse && CollectionUtils.isNotEmpty(transformedHotels)) {
            return liteHotelLists(transformedHotels, sponsoredHotel, utility.isExperimentOn(expDataMap, "CRI"));
        }
        return transformedHotels;
    }

    private boolean isCorp(StaticDetailRequest staticDetailRequest) {
        if (staticDetailRequest!=null && staticDetailRequest.getRequestDetails()!=null
                && StringUtils.isNotBlank(staticDetailRequest.getRequestDetails().getIdContext())) {
            return Constants.CORP_ID_CONTEXT.equalsIgnoreCase(staticDetailRequest.getRequestDetails().getIdContext());
        }
        return false;
    }

    private RoomDetails mapRoomInfoToRoomDetails(com.gommt.hotels.orchestrator.detail.model.response.content.roomInfo.RoomInfo roomInfo, com.gommt.hotels.orchestrator.detail.model.response.content.Media media) {
        if (roomInfo == null) return null;
        RoomDetails details = new RoomDetails();
        details.setRoomCode(roomInfo.getRoomCode());
        details.setRoomName(roomInfo.getRoomName());
        if(StringUtils.isNotEmpty(roomInfo.getRoomSize())) {
            details.setRoomSize(roomInfo.getRoomSize() + SPACE + roomInfo.getRoomSizeUnit());
        }
        details.setParentRoomCode(roomInfo.getParentRoomCode());
        details.setBedCount(roomInfo.getBedCount());
        details.setBedroomCount(roomInfo.getBedRoomCount());
        details.setMaxAdult(roomInfo.getMaxAdultCount());
        details.setMaxGuest(roomInfo.getMaxGuestCount());
        details.setMaxChild(roomInfo.getMaxChildCount());
        details.setRoomViewName(roomInfo.getRoomViewName());
        if (roomInfo.getRoomFlags() != null) {
            details.setMaster(roomInfo.getRoomFlags().isMasterRoom());
        }

        if (MapUtils.isNotEmpty(roomInfo.getRoomArrangementMap()) &&
                roomInfo.getRoomArrangementMap().containsKey(BEDS) &&
                CollectionUtils.isNotEmpty(roomInfo.getRoomArrangementMap().get(BEDS))) {
            List<SleepingArrangement> beds = new ArrayList<>();
            List<ArrangementInfo> bedsArrangementInfos = roomInfo.getRoomArrangementMap().get(BEDS);
            for (ArrangementInfo arrangementInfo : bedsArrangementInfos) {
                SleepingArrangement sleepingArrangement = new SleepingArrangement();
                sleepingArrangement.setType(arrangementInfo.getType());
                sleepingArrangement.setCount(arrangementInfo.getCount());
                beds.add(sleepingArrangement);
            }
            details.setBeds(beds);
        }

        // Map images from professionalMediaEntities by roomCode
        List<String> images = new ArrayList<>();
        if (media != null && MapUtils.isNotEmpty(media.getProfessionalMediaEntities())) {
            for (List<com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity> entityList : media.getProfessionalMediaEntities().values()) {
                if (CollectionUtils.isNotEmpty(entityList)) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity entity : entityList) {
                        //TODO :: Ask Lepsy
                        if (roomInfo.getRoomCode() != null && roomInfo.getRoomCode().equals(entity.getRoomCode())) {
                            String url = entity.getUrl();
                            if (url != null && !url.startsWith("http")) {
                                url = "https:" + url;
                            }
                            images.add(url);
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(images))
            details.setImages(images);
        details.setAmenities(utility.buildAmenities(roomInfo.getAmenities(), roomInfo.getRoomInfoExtension() != null ? roomInfo.getRoomInfoExtension().getStarFacilities() : new ArrayList<>(), roomInfo.getHighlightedAmenities()));
        details.setHighlightedAmenities(utility.getHighlightedAmenities(roomInfo.getHighlightedAmenities()));

        return details;
    }

         /**
      * Detect if property is international - align with reference transformer logic
      */
     private boolean isInternationalProperty(HotelStaticContentResponse source, StaticDetailCriteria searchCriteria) {
         // Check country code from search criteria
         if (searchCriteria != null && searchCriteria.getCountryCode() != null) {
             return !Constants.DOM_COUNTRY.equalsIgnoreCase(searchCriteria.getCountryCode());
         }
         return false; // Default to domestic
     }


    // Phase 1: Convert orchestrator amenities to GI format
    private com.gi.hotels.model.response.staticdata.Amenities convertOrchestratorAmenitiesGI(AmenitiesInfo amenitiesInfo) {
        com.gi.hotels.model.response.staticdata.Amenities amenities = new com.gi.hotels.model.response.staticdata.Amenities();
        
        if (amenitiesInfo == null) {
            return amenities;
        }
        
        // Note: We directly check amenity ratings instead of using external rating maps
        
        // Initialize lists and maps
        //Set<String> allAmenities = new HashSet<>();
        List<com.gi.hotels.model.response.staticdata.Categorized> categorizedSet = new ArrayList<>();
        List<com.gi.hotels.model.response.staticdata.Transformed> popular = new ArrayList<>();
        List<com.gi.hotels.model.response.staticdata.Transformed> rated = new ArrayList<>();

        // 1. Process highlighted amenities for popular node
        if (CollectionUtils.isNotEmpty(amenitiesInfo.getHighlightedAmenities())) {
            for (AmenityGroup group : amenitiesInfo.getHighlightedAmenities()) {
                if (group.getAmenities() != null) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity amenity : group.getAmenities()) {
                        if (amenity.getName() != null) {
                            // Create transformed amenity for popular list
                            com.gi.hotels.model.response.staticdata.Transformed transformedAmenity = createTransformedAmenity(amenity);
                            popular.add(transformedAmenity);
                            
                            // Also check if highlighted amenity has rating > 0.0 for rated node
                            if (amenity.getRating() != null && amenity.getRating() >= 0.0) {
                                rated.add(transformedAmenity);
                            }

                        }
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(amenitiesInfo.getAmenities())) {
            for (AmenityGroup group : amenitiesInfo.getAmenities()) {
                com.gi.hotels.model.response.staticdata.Categorized categorized = new Categorized();
                categorized.setLabel(group.getName());
                categorized.setData(group.getAmenities().stream().map(amenity -> { return amenity.getName();}).collect(Collectors.toList()));
                categorizedSet.add(categorized);

            }
        }

        if (CollectionUtils.isNotEmpty(categorizedSet)) {
            amenities.setCategorized(new ArrayList<>(categorizedSet));
        }
        // Set popular amenities from highlightedAmenities
        if (CollectionUtils.isNotEmpty(popular)) {
            amenities.setPopular(new ArrayList<>(popular));
        }
        
        // Set rated amenities (amenities with rating > 0.0)
        if (CollectionUtils.isNotEmpty(rated)) {
            amenities.setRated(new ArrayList<>(rated));
        }
        
        return amenities;
    }
    

    
    /**
     * Create transformed amenity following the same pattern as buildTransformed in ContentHotelDetailsParserProtoGI
     * This method maps all available fields from orchestrator amenity to the transformed object
     */
    private com.gi.hotels.model.response.staticdata.Transformed createTransformedAmenity(
            com.gommt.hotels.orchestrator.detail.model.response.content.amenity.Amenity amenity) {
        com.gi.hotels.model.response.staticdata.Transformed transformed = new com.gi.hotels.model.response.staticdata.Transformed();
        transformed.setName(amenity.getName());
        // Set rating information (using Double.compare pattern from reference)
        if (amenity.getRating() != null && Double.compare(amenity.getRating(), Double.valueOf(0.0)) > 0) {
            transformed.setRating(amenity.getRating());
        }
        transformed.setSubText(buildL2Amenities(amenity));

        
        return transformed;
    }

    private String buildL2Amenities(Amenity facility) {
        String subText = null;
        if (CollectionUtils.isNotEmpty(facility.getChildAttributes()) && StringUtils.isNotEmpty(facility.getDisplayType())) {
            if ("1".equalsIgnoreCase(facility.getDisplayType())) {
                StringBuilder stringBuilder = new StringBuilder();
                for (AmenityAttribute cf : facility.getChildAttributes()) {
                    stringBuilder.append(cf.getName()).append(Constants.COMMA);
                }
                subText= Constants.AMENITIES_OPEN_BRACE +
                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE;
            }
            if ("2".equalsIgnoreCase(facility.getDisplayType())) {
                StringBuilder stringBuilder = new StringBuilder();
                AmenityAttribute childAttribute = facility.getChildAttributes().get(0);
                stringBuilder.append(childAttribute.getName())
                        .append(Constants.SPACE)
                        .append(Constants.HYPEN)
                        .append(Constants.SPACE);
                if (CollectionUtils.isNotEmpty(childAttribute.getSubAttributes())) {
                    for (AmenitySubAttribute subAttributeFacility : childAttribute.getSubAttributes()) {
                        stringBuilder.append(subAttributeFacility.getName()).append(Constants.COMMA);
                    }
                }
                subText = Constants.AMENITIES_OPEN_BRACE +
                        StringUtils.chop(stringBuilder.toString()) + Constants.AMENITIES_CLOSING_BRACE;
            }
        }
        return subText;
    }

    // Phase 1: Build GI review summary for GI specific requirements
    private JsonNode buildGiReviewSummary(com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary summary) {
        if (summary == null) {
            return null;
        }
        
        // Build a simplified GI review summary object
        Map<String, Object> giSummary = new HashMap<>();
        giSummary.put("source", summary.getSource() != null ? summary.getSource().name() : null);
        giSummary.put("cumulativeRating", summary.getCumulativeRating());
        giSummary.put("totalRatingCount", summary.getTotalRatingCount());
        giSummary.put("totalReviewCount", summary.getTotalReviewCount());
        giSummary.put("ratingText", summary.getRatingText());
        giSummary.put("preferredOTA", summary.isPreferredOTA());
        giSummary.put("crawledData", summary.isCrawledData());
        giSummary.put("selectedCategory", summary.getSelectedCategory());
        
        // Map rating and review breakups
        if (summary.getRatingBreakup() != null) {
            giSummary.put("ratingBreakup", summary.getRatingBreakup());
        }
        if (summary.getReviewBreakup() != null) {
            giSummary.put("reviewBreakup", summary.getReviewBreakup());
        }
        
        // Convert Map to JsonNode using the static ObjectMapper
        return MAPPER.valueToTree(giSummary);
    }

    // Abstract methods implementation from StaticDetailResponseTransformer
    @Override
    protected Map<String, String> buildCardTitleMap() {
        // Implementation for orchestrator flow - can be customized as needed
        Map<String, String> cardTitleMap = new HashMap<>();
        // Add default card titles or customize based on orchestrator requirements
        return cardTitleMap;
    }

    @Override
    protected void addTitleData(com.mmt.hotels.clientgateway.response.staticdetail.HotelResult hotelResult, String countryCode) {
        // Implementation for orchestrator flow - add title data based on country code
        // This method can be customized to add specific title data for orchestrator flow
        if (hotelResult != null) {
            // Add any orchestrator-specific title data modifications here
        }
    }

    @Override
    protected String getLuxeIcon() {
        // Default implementation for orchestrator flow - can be overridden by concrete classes
        return "https://cdn.mmt.com/icons/luxe_default.png";
    }

    // Phase 3: Map policy messages from orchestrator response
    private Map<String, List<String>> mapPolicyMessages(HotelStaticContentResponse source) {
        if (source == null || source.getHotelMetaData() == null) {
            return null;
        }
        
        Map<String, List<String>> policyMessages = new HashMap<>();
        
        // Map from RulesAndPolicies in orchestrator response
        if (source.getHotelMetaData().getRulesAndPolicies() != null) {
            RulesAndPolicies rulesAndPolicies = source.getHotelMetaData().getRulesAndPolicies();
            
            // Add must read rules
            if (CollectionUtils.isNotEmpty(rulesAndPolicies.getMustReadRules())) {
                policyMessages.put("Must Read Rules", rulesAndPolicies.getMustReadRules());
            }
            
            // Add government policies
            if (CollectionUtils.isNotEmpty(rulesAndPolicies.getGovtPolicies())) {
                List<String> govtPolicyTexts = rulesAndPolicies.getGovtPolicies().stream()
                    .filter(Objects::nonNull)
                    .flatMap(policy -> policy.getRules() != null ? policy.getRules().stream() : Stream.empty())
                    .filter(rule -> rule != null && StringUtils.isNotBlank(rule.getText()))
                    .map(rule -> rule.getText())
                    .collect(Collectors.toList());
                if (!govtPolicyTexts.isEmpty()) {
                    policyMessages.put("Government Policies", govtPolicyTexts);
                }
            }
            
            // Add food and dining rules
            if (CollectionUtils.isNotEmpty(rulesAndPolicies.getFoodAndDiningRules())) {
                List<String> foodPolicyTexts = rulesAndPolicies.getFoodAndDiningRules().stream()
                    .filter(Objects::nonNull)
                    .flatMap(policy -> policy.getRules() != null ? policy.getRules().stream() : Stream.empty())
                    .filter(rule -> rule != null && StringUtils.isNotBlank(rule.getText()))
                    .map(rule -> rule.getText())
                    .collect(Collectors.toList());
                if (!foodPolicyTexts.isEmpty()) {
                    policyMessages.put("Food & Dining Rules", foodPolicyTexts);
                }
            }
        }
        
        return policyMessages.isEmpty() ? null : policyMessages;
    }
    
    // Phase 3: Map currency from request criteria with fallback
    private String mapCurrency(HotelStaticContentResponse source, StaticDetailRequest staticDetailRequest) {
        // Priority 1: Get from request criteria
        if (staticDetailRequest != null && staticDetailRequest.getSearchCriteria() != null && 
            StringUtils.isNotBlank(staticDetailRequest.getSearchCriteria().getCurrency())) {
            return staticDetailRequest.getSearchCriteria().getCurrency();
        }
        
        // Priority 2: Default fallback
        return "INR";
    }

    /**
     * Phase 4: Maps legacy image format from orchestrator media to ImageInfo
     */
    private com.mmt.hotels.clientgateway.response.staticdetail.ImageInfo mapLegacyImageInfo(HotelStaticContentResponse source) {
        if (source == null || source.getMedia() == null) {
            return null;
        }
        
        com.mmt.hotels.clientgateway.response.staticdetail.ImageInfo imageInfo = 
            new com.mmt.hotels.clientgateway.response.staticdetail.ImageInfo();
        
        try {
            // Map traveller images
            if (source.getMedia().getTraveller() != null) {
                Map<String, List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail>> travellerMap = 
                    mapTravellerMediaToImageDetails(source.getMedia().getTraveller());
                imageInfo.setTraveller(travellerMap);
            }
            
            // Map professional images
            if (source.getMedia().getProfessionalMediaEntities() != null) {
                Map<String, List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail>> professionalMap = 
                    mapProfessionalMediaToImageDetails(source.getMedia().getProfessionalMediaEntities());
                imageInfo.setProfessional(professionalMap);
            }
            
        } catch (Exception e) {
            LOGGER.error("Error mapping legacy image info from orchestrator", e);
            return null;
        }
        
        return imageInfo;
    }

    /**
     * Helper method to convert traveller media entities to image details
     */
    private Map<String, List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail>> mapTravellerMediaToImageDetails(
            Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity>> mediaMap) {
        
        if (MapUtils.isEmpty(mediaMap)) {
            return null;
        }
        
        Map<String, List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail>> result = new HashMap<>();
        
        for (Map.Entry<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity>> entry : mediaMap.entrySet()) {
            List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail> imageDetails = new ArrayList<>();
            
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                for (com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity mediaEntity : entry.getValue()) {
                    com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail imageDetail = 
                        mapTravellerMediaToImageDetail(mediaEntity);
                    if (imageDetail != null) {
                        imageDetails.add(imageDetail);
                    }
                }
            }
            
            if (!imageDetails.isEmpty()) {
                result.put(entry.getKey(), imageDetails);
            }
        }
        
        return result.isEmpty() ? null : result;
    }

    /**
     * Helper method to convert professional media entities to image details
     */
    private Map<String, List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail>> mapProfessionalMediaToImageDetails(
            Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity>> mediaMap) {
        
        if (MapUtils.isEmpty(mediaMap)) {
            return null;
        }
        
        Map<String, List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail>> result = new HashMap<>();
        
        for (Map.Entry<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity>> entry : mediaMap.entrySet()) {
            List<com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail> imageDetails = new ArrayList<>();
            
            if (CollectionUtils.isNotEmpty(entry.getValue())) {
                for (com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity mediaEntity : entry.getValue()) {
                    com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail imageDetail = 
                        mapProfessionalMediaToImageDetail(mediaEntity);
                    if (imageDetail != null) {
                        imageDetails.add(imageDetail);
                    }
                }
            }
            
            if (!imageDetails.isEmpty()) {
                result.put(entry.getKey(), imageDetails);
            }
        }
        
        return result.isEmpty() ? null : result;
    }

    /**
     * Helper method to convert traveller media entity to ImageDetail
     */
    private com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail mapTravellerMediaToImageDetail(
            com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity mediaEntity) {
        if (mediaEntity == null) {
            return null;
        }
        
        com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail imageDetail = 
            new com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail();
        
        try {
            // Map fields from TravellerMediaEntity
            imageDetail.setUrl(mediaEntity.getUrl());
            imageDetail.setTitle(mediaEntity.getTitle());
            imageDetail.setDate(mediaEntity.getDate());
            imageDetail.setTravellerName(mediaEntity.getTravellerName());
            imageDetail.setImageFilterInfo(mediaEntity.getFilterInfo());
            imageDetail.setRoomCode(mediaEntity.getRoomCode());
            
            // Map additional traveller-specific fields
            if (CollectionUtils.isNotEmpty(mediaEntity.getMmtTagList())) {
                imageDetail.setSeekTags(mediaEntity.getMmtTagList());
            }
            
        } catch (Exception e) {
            LOGGER.debug("Error mapping traveller media entity to image detail", e);
            return null;
        }
        
        return imageDetail;
    }

    /**
     * Helper method to convert professional media entity to ImageDetail
     */
    private com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail mapProfessionalMediaToImageDetail(
            com.gommt.hotels.orchestrator.detail.model.response.content.media.ProfessionalMediaEntity mediaEntity) {
        if (mediaEntity == null) {
            return null;
        }
        
        com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail imageDetail = 
            new com.mmt.hotels.clientgateway.response.staticdetail.ImageDetail();
        
        try {
            // Map fields from ProfessionalMediaEntity
            imageDetail.setUrl(mediaEntity.getUrl());
            imageDetail.setTitle(mediaEntity.getTitle());
            imageDetail.setImageFilterInfo(mediaEntity.getFilterInfo());
            imageDetail.setRoomCode(mediaEntity.getRoomCode());
            
            // Map professional-specific fields
            if (CollectionUtils.isNotEmpty(mediaEntity.getSeekTags())) {
                imageDetail.setSeekTags(mediaEntity.getSeekTags());
            }
            
        } catch (Exception e) {
            LOGGER.debug("Error mapping professional media entity to image detail", e);
            return null;
        }
        
        return imageDetail;
    }

    /**
     * Phase 4: Maps BHF persuasions list from orchestrator response
     */
    private List<com.mmt.hotels.clientgateway.response.BhfPersuasion> mapBhfPersuasions(HotelStaticContentResponse source) {
        if (source == null || source.getHotelMetaData() == null || 
            CollectionUtils.isEmpty(source.getHotelMetaData().getBhfPersuasions())) {
            return new ArrayList<>();
        }
        
        List<com.mmt.hotels.clientgateway.response.BhfPersuasion> bhfPersuasions = new ArrayList<>();
        
        for (com.gommt.hotels.orchestrator.detail.model.response.content.common.PersuasionData orchestratorPersuasion : source.getHotelMetaData().getBhfPersuasions()) {
            if (orchestratorPersuasion != null) {
                com.mmt.hotels.clientgateway.response.BhfPersuasion bhfPersuasion = new com.mmt.hotels.clientgateway.response.BhfPersuasion();
                
                // Map value to text - primary content
                if (StringUtils.isNotBlank(orchestratorPersuasion.getValue())) {
                    bhfPersuasion.setText(orchestratorPersuasion.getValue());
                }
                
                // Map type to name/heading - categorization
                if (StringUtils.isNotBlank(orchestratorPersuasion.getType())) {
                    bhfPersuasion.setName(orchestratorPersuasion.getType());
                    bhfPersuasion.setHeading(orchestratorPersuasion.getType());
                }
                
                // Set default styling for BHF persuasions
                bhfPersuasion.setBgColor("#FFF3CD"); // Light warning yellow background
                bhfPersuasion.setTextColor("#856404"); // Dark yellow text
                bhfPersuasion.setAdditionalTextColor("#6C757D"); // Muted gray for additional text
                
                // Set default CTAs for BHF scenarios
                bhfPersuasion.setLeftCTA("Learn More");
                bhfPersuasion.setRightCTA("OK");
                
                // Set icon based on persuasion type if available
                if (StringUtils.isNotBlank(orchestratorPersuasion.getType())) {
                    String type = orchestratorPersuasion.getType().toLowerCase();
                    if (type.contains("warning") || type.contains("alert")) {
                        bhfPersuasion.setIcon("⚠️");
                    } else if (type.contains("info") || type.contains("information")) {
                        bhfPersuasion.setIcon("ℹ️");
                    } else {
                        bhfPersuasion.setIcon("💡"); // Default info icon
                    }
                }
                
                bhfPersuasions.add(bhfPersuasion);
            }
        }
        
        return bhfPersuasions;
    }

    private com.mmt.hotels.clientgateway.response.BhfPersuasion mapDetailTopBhfPersuasion(HotelStaticContentResponse source) {
        if (source == null || source.getHotelMetaData() == null || 
            CollectionUtils.isEmpty(source.getHotelMetaData().getBhfPersuasions())) {
            return null;
        }
        
        // Look for top-level BHF persuasion (first one or one with specific type)
        com.gommt.hotels.orchestrator.detail.model.response.content.common.PersuasionData topPersuasion = 
            source.getHotelMetaData().getBhfPersuasions().stream()
                .filter(p -> p != null && (StringUtils.isBlank(p.getType()) || 
                    p.getType().toLowerCase().contains("top") || 
                    p.getType().toLowerCase().contains("header")))
                .findFirst()
                .orElse(source.getHotelMetaData().getBhfPersuasions().get(0));
        
        if (topPersuasion == null) {
            return null;
        }
        
        return mapSingleBhfPersuasion(topPersuasion, "TOP_BHF");
    }

    private com.mmt.hotels.clientgateway.response.BhfPersuasion mapDetailRrBhfPersuasion(HotelStaticContentResponse source) {
        if (source == null || source.getHotelMetaData() == null || 
            CollectionUtils.isEmpty(source.getHotelMetaData().getBhfPersuasions())) {
            return null;
        }
        
        // Look for RR (Rate Related) BHF persuasion
        com.gommt.hotels.orchestrator.detail.model.response.content.common.PersuasionData rrPersuasion = 
            source.getHotelMetaData().getBhfPersuasions().stream()
                .filter(p -> p != null && p.getType() != null && 
                    (p.getType().toLowerCase().contains("rr") || 
                     p.getType().toLowerCase().contains("rate") ||
                     p.getType().toLowerCase().contains("price")))
                .findFirst()
                .orElse(null);
        
        if (rrPersuasion == null) {
            return null;
        }
        
        return mapSingleBhfPersuasion(rrPersuasion, "RR_BHF");
    }

    private com.mmt.hotels.clientgateway.response.BhfPersuasion mapDetailBlockerBhfPersuasion(HotelStaticContentResponse source) {
        if (source == null || source.getHotelMetaData() == null || 
            CollectionUtils.isEmpty(source.getHotelMetaData().getBhfPersuasions())) {
            return null;
        }
        
        // Look for Blocker BHF persuasion
        com.gommt.hotels.orchestrator.detail.model.response.content.common.PersuasionData blockerPersuasion = 
            source.getHotelMetaData().getBhfPersuasions().stream()
                .filter(p -> p != null && p.getType() != null && 
                    (p.getType().toLowerCase().contains("blocker") || 
                     p.getType().toLowerCase().contains("block") ||
                     p.getType().toLowerCase().contains("warning")))
                .findFirst()
                .orElse(null);
        
        if (blockerPersuasion == null) {
            return null;
        }
        
        return mapSingleBhfPersuasion(blockerPersuasion, "BLOCKER_BHF");
    }
    
    private com.mmt.hotels.clientgateway.response.BhfPersuasion mapSingleBhfPersuasion(
            com.gommt.hotels.orchestrator.detail.model.response.content.common.PersuasionData orchestratorPersuasion, 
            String persuasionContext) {
        
        if (orchestratorPersuasion == null) {
            return null;
        }
        
        com.mmt.hotels.clientgateway.response.BhfPersuasion bhfPersuasion = new com.mmt.hotels.clientgateway.response.BhfPersuasion();
        
        // Map core content
        if (StringUtils.isNotBlank(orchestratorPersuasion.getValue())) {
            bhfPersuasion.setText(orchestratorPersuasion.getValue());
        }
        
        if (StringUtils.isNotBlank(orchestratorPersuasion.getType())) {
            bhfPersuasion.setName(orchestratorPersuasion.getType());
            bhfPersuasion.setHeading(orchestratorPersuasion.getType());
        }
        
        // Context-specific styling and configuration
        switch (persuasionContext) {
            case "TOP_BHF":
                bhfPersuasion.setBgColor("#E8F4FD"); // Light blue background for top
                bhfPersuasion.setTextColor("#0C5AA6"); // Blue text
                bhfPersuasion.setIcon("📌");
                bhfPersuasion.setLeftCTA("Details");
                bhfPersuasion.setRightCTA("Got it");
                break;
                
            case "RR_BHF":
                bhfPersuasion.setBgColor("#FFF3CD"); // Light yellow for rate-related
                bhfPersuasion.setTextColor("#856404"); // Orange-brown text
                bhfPersuasion.setIcon("💰");
                bhfPersuasion.setLeftCTA("View Rates");
                bhfPersuasion.setRightCTA("OK");
                break;
                
            case "BLOCKER_BHF":
                bhfPersuasion.setBgColor("#F8D7DA"); // Light red for blockers
                bhfPersuasion.setTextColor("#721C24"); // Dark red text
                bhfPersuasion.setIcon("🚫");
                bhfPersuasion.setLeftCTA("Contact Support");
                bhfPersuasion.setRightCTA("Understood");
                break;
                
            default:
                // Default styling
                bhfPersuasion.setBgColor("#F8F9FA"); // Light gray
                bhfPersuasion.setTextColor("#495057"); // Dark gray text
                bhfPersuasion.setIcon("ℹ️");
                bhfPersuasion.setLeftCTA("Learn More");
                bhfPersuasion.setRightCTA("OK");
        }
        
        // Set consistent additional text styling
        bhfPersuasion.setAdditionalTextColor("#6C757D"); // Muted gray for additional text
        
        return bhfPersuasion;
    }

    /**
     * Phase 4: Maps persuasion detail from orchestrator response
     */
    private com.mmt.hotels.pojo.response.detail.persuasion.PersuasionDetail mapPersuasionDetail(HotelStaticContentResponse source) {
        if (source == null) {
            return null;
        }
        
        try {
            com.mmt.hotels.pojo.response.detail.persuasion.PersuasionDetail persuasionDetail = 
                new com.mmt.hotels.pojo.response.detail.persuasion.PersuasionDetail();
            
            Map<String, List<com.mmt.hotels.pojo.response.detail.persuasion.PersuasionData>> persuasionMap = 
                new HashMap<>();
            
            // Map persuasions from chatBotPersuasion if available
            if (source.getChatBotPersuasion() != null) {
                for (Map.Entry<String, com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData> entry : source.getChatBotPersuasion().entrySet()) {
                    List<com.mmt.hotels.pojo.response.detail.persuasion.PersuasionData> persuasionDataList = 
                        mapChatBotPersuasionToDetail(entry.getValue());
                    
                    if (CollectionUtils.isNotEmpty(persuasionDataList)) {
                        persuasionMap.put(entry.getKey(), persuasionDataList);
                    }
                }
            }
            
            persuasionDetail.setPersuasionResponse(persuasionMap.isEmpty() ? null : persuasionMap);
            return persuasionDetail;
            
        } catch (Exception e) {
            LOGGER.error("Error mapping persuasion detail from orchestrator", e);
            return null;
        }
    }

    /**
     * Helper method to map chatbot persuasion data to detail persuasion data
     */
    private List<com.mmt.hotels.pojo.response.detail.persuasion.PersuasionData> mapChatBotPersuasionToDetail(
            com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData persuasionSource) {
        if (persuasionSource == null || persuasionSource.getData() == null) {
            return null;
        }
        
        List<com.mmt.hotels.pojo.response.detail.persuasion.PersuasionData> result = new ArrayList<>();
        
        try {
            for (com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.PersuasionValue persuasionValue : persuasionSource.getData()) {
                if (persuasionValue != null) {
                    com.mmt.hotels.pojo.response.detail.persuasion.PersuasionData persuasionData = 
                        com.mmt.hotels.pojo.response.detail.persuasion.PersuasionData.builder()
                            .text(persuasionValue.getText())
                            .subText(persuasionValue.getSubtext())
                            .icon(persuasionValue.getIconurl())
                            .priority(persuasionValue.getMultiPersuasionPriority() != null ? persuasionValue.getMultiPersuasionPriority() : 0)
                            .build();
                    
                    if (persuasionData != null) {
                        result.add(persuasionData);
                    }
                }
            }
            
        } catch (Exception e) {
            LOGGER.debug("Error mapping chatbot persuasion to detail", e);
        }
        
        return result.isEmpty() ? null : result;
    }

    /**
     * Build a Map<String, ReviewSummary> from a single TravellerReviewSummary, mapping all fields directly.
     *
     * @param summary   The TravellerReviewSummary from orchestrator
     * @param traveller The map of traveller images/media (Map<String, List<TravellerMediaEntity>>)
     */
    public static Map<String, ReviewSummary> buildReviewSummaryMap(
            com.gommt.hotels.orchestrator.detail.model.response.ugc.TravellerReviewSummary summary,
            Map<String, List<com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity>> traveller
    ) {
        if (summary == null || summary.getSource() == null) {
            return null;
        }
        ReviewSummary reviewSummary = new ReviewSummary();
        // Explicit field-by-field mapping
        reviewSummary.setSource(summary.getSource().name());
        reviewSummary.setRecentRatings(summary.getRecentRatings() != null ? new java.util.ArrayList<Object>(summary.getRecentRatings()) : null);
        reviewSummary.setCumulativeRating(summary.getCumulativeRating());
        reviewSummary.setManualPersuasion(mapManualPersuasion(summary.getManualPersuasion()));
        reviewSummary.setTotalReviewCount(summary.getTotalReviewCount());
        reviewSummary.setTotalRatingCount(summary.getTotalRatingCount());
        reviewSummary.setRatingBreakup(summary.getRatingBreakup());
        reviewSummary.setReviewBreakup(summary.getReviewBreakup());
        reviewSummary.setTravelTypes(summary.getTravelTypes());
        reviewSummary.setSortingCriterion(summary.getSortingCriterion());
        reviewSummary.setChatGPTSummaryExists(summary.isChatGPTSummaryExists());
        reviewSummary.setCrawledData(summary.isCrawledData());
        reviewSummary.setRatingText(summary.getRatingText());
        reviewSummary.setBestReviewTitle(summary.getBestReviewTitle());
        reviewSummary.setSelectedCategory(summary.getSelectedCategory());
        reviewSummary.setDisableLowRating(summary.isDisableLowRating());
        reviewSummary.setPreferredOTA(summary.isPreferredOTA());
        reviewSummary.setShowUpvote(summary.isShowUpvote());
        reviewSummary.setHighRatedTopic(summary.getHighRatedTopic());
        reviewSummary.setRatedText(summary.getRatedText());
        reviewSummary.setRatedIcon(summary.getRatedIcon());
        reviewSummary.setMmtReviewCount(summary.getMmtReviewCount());
        reviewSummary.setBest(mapBestReviews(summary.getBestReviews()));
        reviewSummary.setSeekTagDetails(mapSeekTagDetails(summary.getSeekTagDetails()));
        reviewSummary.setHotelRatingSummary(mapTopicRatingsToHotelRatingSummary(summary.getTopicRatings()));
        // Populate imageTypes from TravellerMediaEntity.mediaType in traveller map
        Set<String> imageTypesSet = new HashSet<>();
        if (traveller != null) {
            for (List<com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity> mediaList : traveller.values()) {
                if (mediaList != null) {
                    for (com.gommt.hotels.orchestrator.detail.model.response.content.media.TravellerMediaEntity media : mediaList) {
                        if (media.getMediaType() != null) {
                            imageTypesSet.add(media.getMediaType());
                        }
                    }
                }
            }
        }
        reviewSummary.setImageTypes(new ArrayList<>(imageTypesSet));
        // Set disclaimer from summary
        if (summary.getDisclaimer() != null) {
            reviewSummary.setDisclaimer(MAPPER.valueToTree(summary.getDisclaimer()));
        }
        Map<String, ReviewSummary> map = new HashMap<>();
        map.put(summary.getSource().name(), reviewSummary);
        return map;
    }

    private static List<ReviewObject> mapBestReviews(List<com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewDescription> bestReviews) {
        if (bestReviews == null) return null;
        List<ReviewObject> best = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewDescription r : bestReviews) {
            ReviewObject ro = new ReviewObject();
            ro.setPublishDate(r.getPublishDate());
            ro.setTravellerName(r.getTravellerName());
            ro.setTitle(r.getTitle());
            ro.setCheckoutDate(r.getCheckoutDate());
            ro.setRating(r.getRating());
            ro.setReviewText(r.getReviewText());
            ro.setCheckinDate(r.getCheckinDate());
            ro.setId(r.getId());
            // If getTravelType() is not present, set to null
            ro.setTravelType(null);
            ro.setUpvoted(r.isUpvote());
            best.add(ro);
        }
        return best;
    }

    private static SeekTagDetails mapSeekTagDetails(com.gommt.hotels.orchestrator.detail.model.response.ugc.SeekTagDetails src) {
        if (src == null) return null;
        SeekTagDetails dest = new SeekTagDetails();
        dest.setSeekTagsTitle(src.getTitle());
        dest.setSeekTagsSubtitle(src.getSubtitle());
        dest.setSeekTagIcon(src.getIcon());
        dest.setSeekTagSummary(src.getSummary());
        dest.setSeekTagSpans(src.getSpans());
        if (src.getMaxSeekTagCount() != null) dest.setMaxSeekTagCount(src.getMaxSeekTagCount());
        if (src.getDefaultSeekTagCount() != null) dest.setDefaultSeekTagCount(src.getDefaultSeekTagCount());
        // Map topicSummary to topicSummaryList
        if (src.getTopicSummary() != null && !src.getTopicSummary().isEmpty()) {
            List<com.mmt.hotels.clientgateway.response.TopicSummary> topicList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.ugc.SeekTagDetails.SeekTagTopicSummary t : src.getTopicSummary()) {
                com.mmt.hotels.clientgateway.response.TopicSummary ts = new com.mmt.hotels.clientgateway.response.TopicSummary();
                ts.setConcept(t.getConcept());
                ts.setSummary(t.getSummary());
                topicList.add(ts);
            }
            dest.setTopicSummaryList(topicList);
        }
        return dest;
    }

    /**
     * Map a list of TopicRating (orchestrator) to a list of ConceptSummary (clientgateway)
     */
    private static List<com.mmt.hotels.clientgateway.response.ConceptSummary> mapTopicRatingsToHotelRatingSummary(
            List<com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating> topicRatings
    ) {
        if (topicRatings == null) return null;
        List<com.mmt.hotels.clientgateway.response.ConceptSummary> result = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating t : topicRatings) {
            com.mmt.hotels.clientgateway.response.ConceptSummary c = new com.mmt.hotels.clientgateway.response.ConceptSummary();
            c.setConcept(t.getConcept());
            c.setDisplayText(t.getDisplayText() != null ? t.getDisplayText() : t.getTitle());
            c.setValue(t.getValue() != 0 ? t.getValue() : t.getRating());
            c.setShow(t.isShow());
            c.setReviewCount(t.getReviewCount());
            c.setHeroTag(t.isHeroTag());
            // Map tags to subConcepts
            if (t.getTags() != null) {
                List<com.mmt.hotels.clientgateway.response.SubConcept> subConcepts = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.ugc.Tag tag : t.getTags()) {
                    com.mmt.hotels.clientgateway.response.SubConcept s = new com.mmt.hotels.clientgateway.response.SubConcept();
                    s.setSentiment(tag.getSentiment());
                    s.setSubConcept(tag.getName());
                    s.setRelatedReviewCount(tag.getCount());
                    s.setPriorityScore(tag.getValue() != 0 ? tag.getValue() : (int) tag.getRating());
                    // Optionally set displayText, tagType, source if available
                    s.setDisplayText(tag.getName());
                    subConcepts.add(s);
                }
                c.setSubConcepts(subConcepts);
            }
            result.add(c);
        }
        return result;
    }

    // Helper to map DisplayItem to ManualPersuasion
    private static ManualPersuasion mapManualPersuasion(com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem src) {
        if (src == null) return null;
        ManualPersuasion dest = new ManualPersuasion();
        dest.setIconUrl(src.getIconUrl());
        dest.setText(src.getText());
        return dest;
    }

    /**
     * Concrete method to remove icons from staff info, similar to StaticDetailResponseTransformer
     */
    public void removeIcon(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffInfo staffInfo) {
        if (staffInfo == null) {
            return;
        }
        removeIcon(staffInfo.getHost());
        removeIcon(staffInfo.getCook());
        removeIcon(staffInfo.getCaretaker());
    }

    private void removeIcon(com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.Staff staff) {
        if (staff == null) {
            return;
        }
        if (staff.getData() != null) {
            for (com.gommt.hotels.orchestrator.detail.model.response.content.staffinfo.StaffData staffData : staff.getData()) {
                if (CollectionUtils.isNotEmpty(staffData.getGeneralInfo())) {
                    staffData.getGeneralInfo().forEach(x -> x.setIconUrl(null));
                }
            }
        }
    }

    private ComparatorResponse buildChainComparatorResponse(StaticDetailRequest staticDetailRequest, SearchHotelsRequest searchHotelsRequest, com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse hotelCompareResponse,
                                                            Map<String, String> expDataMap,
                                                            boolean isLiteResponse, CommonModifierResponse commonModifierResponse) {

        ComparatorResponse comparatorResponse = new ComparatorResponse();
        if (hotelCompareResponse == null) {
            return comparatorResponse;
        }
        List<Hotel> finalList = getListingHotels(searchHotelsRequest, hotelCompareResponse,
                commonModifierResponse, expDataMap, isLiteResponse, false);
        comparatorResponse.setHotelList(finalList);
        return comparatorResponse;
    }

    public List<Hotel> liteHotelLists(List<Hotel> hotelList, boolean isSponsoredHotels, boolean isChainInfoRequired) {
        List<Hotel> finalHotelList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hotelList)) {
            for (Hotel hotel : hotelList) {
                Hotel hotelLite = new Hotel();
                hotelLite.setId(hotel.getId());
                hotelLite.setName(hotel.getName());
                hotelLite.setStarRating(hotel.getStarRating());
                hotelLite.setStarRatingType(hotel.getStarRatingType());
                hotelLite.setHighSellingAltAcco(hotel.getHighSellingAltAcco());
                hotelLite.setSoldOut(hotel.isSoldOut());
                hotelLite.setIsAltAcco(hotel.getIsAltAcco());
                hotelLite.setPriceDetail(hotel.getPriceDetail());
                hotelLite.setLocationDetail(hotel.getLocationDetail());
                hotelLite.setDetailDeeplinkUrl(hotel.getDetailDeeplinkUrl());
                hotelLite.setLocationPersuasion(hotel.getLocationPersuasion());
                hotelLite.setRatePersuasionText(hotel.getRatePersuasionText());
                hotelLite.setHotelPersuasions(hotel.getHotelPersuasions());
                //hotelLite.setFacilitiesConfigMap(hotel.getFacilitiesConfigMap());
                if (CollectionUtils.isNotEmpty(hotel.getMedia())) {
                    for (MediaInfo liteMediaInfo : hotel.getMedia()) {
                        if ("IMAGE".equalsIgnoreCase(liteMediaInfo.getMediaType())) {
                            hotelLite.setMedia(Collections.singletonList(liteMediaInfo));
                            break;
                        }
                    }
                }
                if (hotel.getReviewSummary() != null) {
                    ReviewSummary liteReviewSummary = new ReviewSummary();
                    liteReviewSummary.setSource(hotel.getReviewSummary().getSource());
                    liteReviewSummary.setCumulativeRating(hotel.getReviewSummary().getCumulativeRating());
                    liteReviewSummary.setTotalRatingCount(hotel.getReviewSummary().getTotalRatingCount());
                    liteReviewSummary.setRatingText(hotel.getReviewSummary().getRatingText());
                    liteReviewSummary.setPreferredOTA(hotel.getReviewSummary().isPreferredOTA());
                    hotelLite.setReviewSummary(liteReviewSummary);
                }
                if (isSponsoredHotels || isChainInfoRequired) {
                    List<String> requiredPlaceHolders = Arrays.asList("PLACEHOLDER_CARD_M4", "PLACEHOLDER_IMAGE_LEFT_TOP");
                    if (isChainInfoRequired) {
                        requiredPlaceHolders = Arrays.asList("PLACEHOLDER_CARD_M2");
                    }
                    Map<String, Object> hotelPersuasions = (Map<String, Object>) hotel.getHotelPersuasions();
                    if (hotelPersuasions != null) {
                        Map<String, Object> liteHotelPersuasions = new LinkedHashMap<>();
                        for (String key : requiredPlaceHolders) {
                            if (hotelPersuasions.containsKey(key)) {
                                liteHotelPersuasions.put(key, hotelPersuasions.get(key));
                            }
                        }
                        if (MapUtils.isNotEmpty(liteHotelPersuasions)) {
                            hotelLite.setHotelPersuasions(liteHotelPersuasions);
                        }
                    }
                }
                finalHotelList.add(hotelLite);
            }
        }
        return finalHotelList;
    }

    /**
     * Maps a list of TopicRating from orchestrator to a list of RatingSummaryGI for client gateway
     * 
     * TopicRating fields mapped:
     * - concept ✓ → concept
     * - displayText ✓ → displayText (with fallback to title)
     * - value ✓ → value (with fallback to rating)
     * - iconUrl ✓ → iconUrl
     * - sentiment ✓ → sentiment
     * - show ✓ → toBeDisplayed
     * - tags ✓ → tags (mapped via mapTagToGiTag)
     * 
     * TopicRating fields NOT mapped (no corresponding field in RatingSummaryGI):
     * - reviewCount: No equivalent field in RatingSummaryGI
     * - heroTag: No equivalent field in RatingSummaryGI  
     * - display: No equivalent field in RatingSummaryGI
     * - title: Used as fallback for displayText
     * - rating: Used as fallback for value
     *
     * @param topicRatings List of TopicRating from orchestrator
     * @return List of RatingSummaryGI for client gateway
     */
    private List<com.mmt.hotels.model.response.flyfish.RatingSummaryGI> mapTopicRatingsToRatingSummaryGI(
            List<com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating> topicRatings) {
        if (topicRatings == null || topicRatings.isEmpty()) {
            return null;
        }

        List<com.mmt.hotels.model.response.flyfish.RatingSummaryGI> ratingSummaryGIList = new ArrayList<>();

        for (com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating topicRating : topicRatings) {
            com.mmt.hotels.model.response.flyfish.RatingSummaryGI ratingSummaryGI =
                new com.mmt.hotels.model.response.flyfish.RatingSummaryGI();

            // Map all available fields from TopicRating to RatingSummaryGI
            ratingSummaryGI.setConcept(topicRating.getConcept());

            // Use displayText, fallback to title if displayText is null/empty
            String displayText = StringUtils.isNotBlank(topicRating.getDisplayText()) ?
                topicRating.getDisplayText() : topicRating.getTitle();
            ratingSummaryGI.setDisplayText(displayText);

            // Use value, fallback to rating if value is 0
            ratingSummaryGI.setValue(topicRating.getValue() != 0 ? (int) topicRating.getValue() : (int) topicRating.getRating());

            ratingSummaryGI.setIconUrl(topicRating.getIconUrl());
            ratingSummaryGI.setSentiment(topicRating.getSentiment());

            // Map show to toBeDisplayed, consider both show and display flags
            boolean shouldDisplay = topicRating.isDisplay();
            ratingSummaryGI.setToBeDisplayed(shouldDisplay);

            // Map tags from List<Tag> to List<GiTag>
            if (topicRating.getTags() != null && !topicRating.getTags().isEmpty()) {
                List<com.mmt.hotels.model.response.flyfish.GiTag> giTags = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.ugc.Tag tag : topicRating.getTags()) {
                    com.mmt.hotels.model.response.flyfish.GiTag giTag = mapTagToGiTag(tag);
                    if (giTag != null) {
                        giTags.add(giTag);
                    }
                }
                ratingSummaryGI.setTags(giTags);
            }

            // Note: reviewCount and heroTag from TopicRating cannot be mapped as
            // RatingSummaryGI does not have corresponding fields

            ratingSummaryGIList.add(ratingSummaryGI);
        }

        return ratingSummaryGIList;
    }

    /**
     * Maps a Tag from orchestrator to a GiTag for client gateway
     * 
     * Tag fields mapped (ALL fields covered):
     * - name ✓ → name
     * - value ✓ → value 
     * - sentiment ✓ → sentiment
     * - rating ✓ → rating
     * - count ✓ → count
     * 
     * All Tag fields are successfully mapped to GiTag fields.
     *
     * @param tag Tag from orchestrator
     * @return GiTag for client gateway
     */
    private com.mmt.hotels.model.response.flyfish.GiTag mapTagToGiTag(
            com.gommt.hotels.orchestrator.detail.model.response.ugc.Tag tag) {
        if (tag == null) {
            return null;
        }

        com.mmt.hotels.model.response.flyfish.GiTag giTag = new com.mmt.hotels.model.response.flyfish.GiTag();

        // Map all fields from Tag to GiTag (complete 1:1 mapping)
        giTag.setName(tag.getName());
        giTag.setValue(tag.getValue());
        giTag.setSentiment(tag.getSentiment());
        giTag.setRating(tag.getRating());
        giTag.setCount(tag.getCount());

        return giTag;
    }

    public UGCSummary buildUgcReviewSummary(TravellerReviewSummary summary, CommonModifierResponse commonModifierResponse, String countryCode) {
        if (summary == null) {
            return null;
        }

        UGCSummary ugcSummary = new UGCSummary();

        // 1. Set card title - using a default title for now
        ugcSummary.setCardTitle(REVIEW_RATING_TITLE);

        // 3. Populate UGCPlatformReviewSummaryDTO
        com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO platformData =
                new com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO();

        // Map source OTA
        if (summary.getSource() != null) {
            try {
                platformData.setSource(com.mmt.hotels.model.request.flyfish.OTA.valueOf(summary.getSource().name()));
            } catch (IllegalArgumentException e) {
                // Set default or skip if enum doesn't match
            }
        }

        platformData.setTravelTypeList(summary.getTravelTypeList());

        // Map available OTAs
        if (summary.getAvailableOTAs() != null && !summary.getAvailableOTAs().isEmpty()) {
            List<com.mmt.hotels.model.request.flyfish.OTA> availableOtaList = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.enums.OTA orchestratorOta : summary.getAvailableOTAs()) {
                try {
                    com.mmt.hotels.model.request.flyfish.OTA clientOta =
                            com.mmt.hotels.model.request.flyfish.OTA.valueOf(orchestratorOta.name());
                    availableOtaList.add(clientOta);
                } catch (IllegalArgumentException e) {
                    // Skip if OTA enum value doesn't exist
                }
            }
            platformData.setAvailableOTAs(availableOtaList);
        }

        // Map basic fields
        platformData.setShowUpvote(summary.isShowUpvote());
        platformData.setCrawledData(summary.isCrawledData());
        platformData.setDisableLowRating(summary.isDisableLowRating());
        platformData.setChatGPTSummaryExists(summary.isChatGPTSummaryExists());
        platformData.setPreferredOTA(summary.isPreferredOTA());
        if(summary.getExtRating() != null) {
            ExtRating extRating = new ExtRating();
            extRating.setHeader(summary.getExtRating().getHeader());
            extRating.setDescriptionTitle(summary.getExtRating().getDescriptionTitle());
            extRating.setDescriptionInfo(summary.getExtRating().getDescriptionInfo());
            platformData.setExtRating(extRating);
        }
        if (summary.isNewListing()) platformData.setIsNewListing(summary.isNewListing());

        // Map review and rating counts
        platformData.setReviewCount(summary.getReviewCount());
        platformData.setRatingCount(summary.getRatingCount());
        platformData.setTotalReviewsCount(summary.getTotalReviewCount());
        platformData.setTotalRatingCount(summary.getTotalRatingCount());

        List<ReviewSortingCriterionListDTO> sortingCriterionListDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(summary.getSortingCriteriaList())) {
            for (SortingCriteria sortingCriteria : summary.getSortingCriteriaList()) {
                if (sortingCriteria != null) {
                    ReviewSortingCriterionListDTO reviewSortingCriterionListDTO = new ReviewSortingCriterionListDTO();
                    reviewSortingCriterionListDTO.setCriteriaType(sortingCriteria.getCriteriaType());
                    reviewSortingCriterionListDTO.setDisplayText(sortingCriteria.getDisplayText());
                    sortingCriterionListDTOList.add(reviewSortingCriterionListDTO);
                }
            }
        }

        platformData.setSortingCriterionList(sortingCriterionListDTOList);

        // Map MMT review count
        if (summary.getMmtReviewCount() > 0) {
            platformData.setMmtReviewCount(summary.getMmtReviewCount());
        }

        // Map rating data
        platformData.setCumulativeRating(summary.getCumulativeRating());
        platformData.setRatingText(summary.getRatingText());
        platformData.setRecentRatings(summary.getRecentRatings() != null ?
                summary.getRecentRatings().stream().map(Float::intValue).collect(Collectors.toList()) : null);

        // Map rated text and icon
        platformData.setRatedText(summary.getRatedText());
        platformData.setRatedIcon(summary.getRatedIcon());

        // Map high rated topics
        platformData.setHighRatedTopic(summary.getHighRatedTopic());

        // Map best review title
        platformData.setBestReviewTitle(summary.getBestReviewTitle());

        // Map breakup maps
        platformData.setRatingBreakup(summary.getRatingBreakup());
        platformData.setReviewBreakup(summary.getReviewBreakup());

        // Map travel types and selected category
        platformData.setTravelTypes(summary.getTravelTypes());
        platformData.setSelectedCategory(summary.getSelectedCategory());

        // Map sorting criterion
        platformData.setSortingCriterion(summary.getSortingCriterion());

        // Map best reviews
        if (summary.getBestReviews() != null && !summary.getBestReviews().isEmpty()) {
            List<com.mmt.hotels.model.response.flyfish.ReviewDescriptionDTO> bestReviewDTOs = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.ugc.ReviewDescription review : summary.getBestReviews()) {
                if (review != null) {
                    com.mmt.hotels.model.response.flyfish.ReviewDescriptionDTO reviewDTO =
                            new com.mmt.hotels.model.response.flyfish.ReviewDescriptionDTO();
                    reviewDTO.setId(review.getId());
                    reviewDTO.setTitle(review.getTitle());
                    reviewDTO.setReviewText(review.getReviewText());
                    reviewDTO.setRating(review.getRating());
                    reviewDTO.setTravellerName(review.getTravellerName());
                    reviewDTO.setPublishDate(review.getPublishDate());
                    reviewDTO.setCheckinDate(review.getCheckinDate());
                    reviewDTO.setCheckoutDate(review.getCheckoutDate());
                    reviewDTO.setUpvoted(review.isUpvote());
                    reviewDTO.setLogo(review.getLogo());
                    reviewDTO.setTravelType(review.getTravelType());
                    // Note: TravelType is not available in orchestrator ReviewDescription
                    bestReviewDTOs.add(reviewDTO);
                }
            }
            platformData.setBest(bestReviewDTOs);
        }

        // Map topic ratings to hotel rating summary
        if (summary.getTopicRatings() != null && !summary.getTopicRatings().isEmpty()) {
            List<com.mmt.hotels.model.response.flyfish.PlatformTopicRatings> platformTopicRatings = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.ugc.TopicRating topicRating : summary.getTopicRatings()) {
                if (topicRating != null) {
                    com.mmt.hotels.model.response.flyfish.PlatformTopicRatings platformRating =
                            new com.mmt.hotels.model.response.flyfish.PlatformTopicRatings();
                    platformRating.setConcept(topicRating.getConcept());
                    platformRating.setDisplayText(topicRating.getDisplayText() != null ?
                            topicRating.getDisplayText() : topicRating.getTitle());
                    platformRating.setValue(topicRating.getValue() != 0 ?
                            topicRating.getValue() : topicRating.getRating());
                    platformRating.setShow(topicRating.isShow());
                    platformRating.setReviewCount(topicRating.getReviewCount());
                    platformRating.setHeroTag(topicRating.isHeroTag());

                    platformTopicRatings.add(platformRating);
                }
            }
            platformData.setHotelRatingSummary(platformTopicRatings);
        }

        // Map sub concepts
        if (summary.getSubConcepts() != null && !summary.getSubConcepts().isEmpty()) {
            List<com.mmt.hotels.model.response.flyfish.SubConceptDTO> subConceptDTOs = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.ugc.SubConcept subConcept : summary.getSubConcepts()) {
                if (subConcept != null) {
                    com.mmt.hotels.model.response.flyfish.SubConceptDTO subConceptDTO =
                            new com.mmt.hotels.model.response.flyfish.SubConceptDTO();
                    subConceptDTO.setSubConcept(subConcept.getSubConcept());
                    subConceptDTO.setSentiment(subConcept.getSentiment());
                    subConceptDTO.setRelatedReviewCount(subConcept.getRelatedReviewCount());
                    subConceptDTO.setPriorityScore(subConcept.getPriorityScore());
                    subConceptDTO.setDisplayText(subConcept.getDisplayText());
                    // Set source and tagType from orchestrator if available
                    if (subConcept.getSource() != null) {
                        subConceptDTO.setSource(subConcept.getSource());
                    }
                    if (subConcept.getTagType() != null) {
                        subConceptDTO.setTagType(subConcept.getTagType());
                    }
                    subConceptDTOs.add(subConceptDTO);
                }
            }
            platformData.setSubConcepts(subConceptDTOs);
        }

        // Map manual persuasion
        if (summary.getManualPersuasion() != null) {
            com.mmt.hotels.model.response.flyfish.ManualPersuasion manualPersuasion =
                    new com.mmt.hotels.model.response.flyfish.ManualPersuasion();
            manualPersuasion.setIconUrl(summary.getManualPersuasion().getIconUrl());
            manualPersuasion.setText(summary.getManualPersuasion().getText());
            platformData.setManualPersuasion(manualPersuasion);
        }

        // Map rating highlight
        if (summary.getRatingHighlight() != null && summary.getRatingHighlight().getRating() != null && summary.getRatingHighlight().getRating() != 0) {
            com.mmt.hotels.model.response.flyfish.RatingHighlight ratingHighlight =
                    new com.mmt.hotels.model.response.flyfish.RatingHighlight();
            ratingHighlight.setRating(summary.getRatingHighlight().getRating());
            ratingHighlight.setTitle(summary.getRatingHighlight().getTitle());
            ratingHighlight.setText(summary.getRatingHighlight().getText());
            platformData.setRatingHighlight(ratingHighlight);
        }

        // Map disclaimer
        if (summary.getDisclaimer() != null) {
            com.mmt.hotels.model.response.flyfish.ReviewSummaryDisclaimer disclaimer =
                    new com.mmt.hotels.model.response.flyfish.ReviewSummaryDisclaimer();
            disclaimer.setFull(summary.getDisclaimer().getFull());
            disclaimer.setSmall(summary.getDisclaimer().getSmall());
            platformData.setDisclaimer(disclaimer);
        }

        // Map seek tag details
        if (summary.getSeekTagDetails() != null) {
            SeekTagDetailsDTO seekTagDetails = new SeekTagDetailsDTO();
            seekTagDetails.setSeekTagsTitle(summary.getSeekTagDetails().getTitle());
            seekTagDetails.setSeekTagsSubtitle(summary.getSeekTagDetails().getSubtitle());
            seekTagDetails.setSeekTagIcon(summary.getSeekTagDetails().getIcon());
            seekTagDetails.setSeekTagSummary(summary.getSeekTagDetails().getSummary());
            seekTagDetails.setSeekTagSpans(summary.getSeekTagDetails().getSpans());

            if (summary.getSeekTagDetails().getMaxSeekTagCount() != null) {
                seekTagDetails.setMaxSeekTagCount(summary.getSeekTagDetails().getMaxSeekTagCount());
            }
            if (summary.getSeekTagDetails().getDefaultSeekTagCount() != null) {
                seekTagDetails.setDefaultSeekTagCount(summary.getSeekTagDetails().getDefaultSeekTagCount());
            }

            // Map topic summary list
            if (summary.getSeekTagDetails().getTopicSummary() != null &&
                    !summary.getSeekTagDetails().getTopicSummary().isEmpty()) {
                List<SeekTagTopicSummaryDTO> topicSummaryList = new ArrayList<>();
                for (com.gommt.hotels.orchestrator.detail.model.response.ugc.SeekTagDetails.SeekTagTopicSummary topicSummary :
                        summary.getSeekTagDetails().getTopicSummary()) {
                    if (topicSummary != null) {
                        SeekTagTopicSummaryDTO mappedSummary =
                                new SeekTagTopicSummaryDTO();
                        mappedSummary.setConcept(topicSummary.getConcept());
                        mappedSummary.setSummary(topicSummary.getSummary());
                        topicSummaryList.add(mappedSummary);
                    }
                }
                seekTagDetails.setSeekTagTopicSummary(topicSummaryList);
            }

                    //setReviewSummaryList
        if (summary.getPersuasionMap() != null && !summary.getPersuasionMap().isEmpty() && summary.getPersuasionMap().get(Constants.UGC_WASHROOM) != null) {
            HotelPersuasionData data = summary.getPersuasionMap().get(Constants.UGC_WASHROOM);
            List<PersuasionValue> persuasionData = data != null ? data.getData() : null;

                if (persuasionData != null && !persuasionData.isEmpty()) {
                    String reviewSummaryHeading = polyglotService.getTranslatedData(Constants.WASHROOM_REVIEW_HEADER);

                    List<ReviewSummaryListItem> reviews = new ArrayList<ReviewSummaryListItem>();
                    // Review Summary List
                    List<String> values = persuasionData.stream()
                            .filter(Objects::nonNull)
                            .map(PersuasionValue::getText)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    String reviewSummaryText = "<b>" + reviewSummaryHeading + "</b> " + String.join(", ", values) + ".";

                    List<String> ids = persuasionData.stream()
                            .filter(Objects::nonNull)
                            .map(PersuasionValue::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    String persuasionIds = String.join("_", ids);
                    com.mmt.hotels.model.request.flyfish.ReviewSummaryListItem reviewSummaryListItem =
                            new com.mmt.hotels.model.request.flyfish.ReviewSummaryListItem();
                    reviewSummaryListItem.setSummary(reviewSummaryText);
                    String washRoomImageTag = mobConfigHelper.getWashRoomReviewImageTag();

                    reviewSummaryListItem.setImageTag(washRoomImageTag);
                    reviewSummaryListItem.setType(Constants.UGC_WASHROOM);
                    reviewSummaryListItem.setTrackingStr(persuasionIds);


                    // Feedback
                    ReviewSummaryFeedback feedback = new ReviewSummaryFeedback();
                    feedback.setLabel(Constants.FEEDBACK_TEXT);
                    feedback.setToast(Constants.TOAST_TEXT);
                    seekTagDetails.setFeedback(feedback);


                    reviews.add(reviewSummaryListItem);
                    seekTagDetails.setReviewsSummaryList(reviews);
                }
            }

            platformData.setSeekTagDetails(seekTagDetails);
        }
        platformData.setRatingSummaryGI(mapTopicRatingsToRatingSummaryGI(summary.getTopicRatings()));

        if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, MMT_RATINGS_ON_GI), TRUE)) {
            if (platformData != null && platformData.getAvailableOTAs() != null) {
                if (platformData.getAvailableOTAs().contains(OTA.MMT)) {
                    clearRatingSummaryAndReviews(platformData);
                }
            }
        }

        // Below Change made for Combined OTA Flow [GIHTL-16802]
        if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, COMBINED_OTA_FLOW), FALSE)) {
            changeUGCDataSource(platformData);
        }

        if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, COMBINED_OTA_FLOW), TRUE)) {
            hideRatingBreakUpForIH(platformData, countryCode);
        }

        if (StringUtils.equalsIgnoreCase(Utility.getExperimentValue(commonModifierResponse, TRAVELLER_IMPRESSIONS_EXP), FALSE)) {
            hideSeekTagDetailsInfo(platformData);
        }

        // Set the populated platform data
        ugcSummary.setData(platformData);

        return ugcSummary;
    }

    protected void clearRatingSummaryAndReviews(com.mmt.hotels.model.response.flyfish.UGCPlatformReviewSummaryDTO summary) {
        summary.setRatingSummaryGI(null);
        summary.setReviewCount(null);
        summary.setRatingHighlight(null);
    }

    protected void changeUGCDataSource(UGCPlatformReviewSummaryDTO data) {
        if (StringUtils.equalsIgnoreCase(data.getSource().getValue(), com.mmt.hotels.model.request.flyfish.OTA.GI_EXP.getValue()) || StringUtils.equalsIgnoreCase(data.getSource().getValue(), com.mmt.hotels.model.request.flyfish.OTA.GI_BKG.getValue())) {
            data.setSource(com.mmt.hotels.model.request.flyfish.OTA.GI);
        }
    }

    protected void hideRatingBreakUpForIH(UGCPlatformReviewSummaryDTO data, String countryCode) {
        if (StringUtils.isNotBlank(countryCode) && !StringUtils.equalsIgnoreCase(Constants.DOM_COUNTRY, countryCode)) {
            data.setRatingBreakup(null);
        }
    }

    protected void hideSeekTagDetailsInfo(UGCPlatformReviewSummaryDTO summaryDTO) {
        if (summaryDTO != null) {
            summaryDTO.setSeekTagDetails(null);
        }
    }

    public List<LiteResponseTravellerImage> getMediaV2TravellerMediaList(List<Tag> tags) {
        List<LiteResponseTravellerImage> travellerImageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Tag tag : tags) {
                if (CollectionUtils.isNotEmpty(tag.getSubtags())) {
                    for (Subtag subtag : tag.getSubtags()) {
                        if (subtag.getData() != null) {
                            for (ImageData item : subtag.getData()) {
                                if (StringUtils.isNotEmpty(item.getMediaType()) && "IMAGE".equalsIgnoreCase(item.getMediaType())) {
                                    LiteResponseTravellerImage liteResponseTravellerImage = new LiteResponseTravellerImage();
                                    liteResponseTravellerImage.setUrl(item.getUrl());
                                    liteResponseTravellerImage.setMediaType(item.getMediaType());
                                    liteResponseTravellerImage.setTitle(item.getTitle());
                                    liteResponseTravellerImage.setFilterInfo(subtag.getName());
                                    liteResponseTravellerImage.setSuperTag(tag.getName());
                                    liteResponseTravellerImage.setAccess(subtag.getAccess());
                                    liteResponseTravellerImage.setAccessType(subtag.getAccessType());
                                    liteResponseTravellerImage.setThumbnailURL(item.getThumbnailURL());
                                    liteResponseTravellerImage.setDate(item.getDate());
                                    liteResponseTravellerImage.setTravelerName(item.getTravelerName());
                                    liteResponseTravellerImage.setDescription(item.getDescription());
                                    liteResponseTravellerImage.setText(subtag.getText());
                                    travellerImageList.add(liteResponseTravellerImage);
                                }
                            }
                        }
                    }
                }
            }
        }
        return travellerImageList;
    }


    public int getMediaV2HotelMediaListCount(List<Tag> tags) {
        int hotelImageLength = 0;
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Tag tag : tags) {
                if (CollectionUtils.isNotEmpty(tag.getSubtags())) {
                    for (Subtag subtag : tag.getSubtags()) {
                        if (subtag.getData() != null) {
                            for (ImageData item : subtag.getData()) {
                                if (StringUtils.isNotEmpty(item.getMediaType()) && !item.getMediaType().equalsIgnoreCase("VIDEO")) {
                                    hotelImageLength++;
                                }
                            }
                        }
                    }
                }
            }
        }
        return hotelImageLength;
    }

    /**
     * Transforms StaticDetailRequest to SearchHotelsRequest for use in buildPersonalizedHotels method
     *
     * @param staticDetailRequest The static detail request to transform
     * @return SearchHotelsRequest with mapped fields from StaticDetailRequest
     */
    private SearchHotelsRequest transformStaticDetailRequestToSearchHotelsRequest(StaticDetailRequest staticDetailRequest) {
        if (staticDetailRequest == null || staticDetailRequest.getSearchCriteria() == null) {
            return null;
        }

        SearchHotelsRequest searchHotelsRequest = new SearchHotelsRequest();

        // Map basic fields
        searchHotelsRequest.setCorrelationKey(staticDetailRequest.getCorrelationKey());
        searchHotelsRequest.setExpData(staticDetailRequest.getExpData());
        searchHotelsRequest.setExpVariantKeys(staticDetailRequest.getExpVariantKeys());
        searchHotelsRequest.setExpDataMap(staticDetailRequest.getExpDataMap());

        // Map device details
        if (staticDetailRequest.getDeviceDetails() != null) {
            searchHotelsRequest.setDeviceDetails(staticDetailRequest.getDeviceDetails());
        }

        // Map request details
        if (staticDetailRequest.getRequestDetails() != null) {
            searchHotelsRequest.setRequestDetails(staticDetailRequest.getRequestDetails());
        }

        // Map feature flags
        if (staticDetailRequest.getFeatureFlags() != null) {
            searchHotelsRequest.setFeatureFlags(staticDetailRequest.getFeatureFlags());
        }

        // Transform StaticDetailCriteria to SearchHotelsCriteria
        if (staticDetailRequest.getSearchCriteria() != null) {
            StaticDetailCriteria staticCriteria = staticDetailRequest.getSearchCriteria();
            SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();

            // Map basic search fields
            searchCriteria.setCheckIn(staticCriteria.getCheckIn());
            searchCriteria.setCheckOut(staticCriteria.getCheckOut());
            searchCriteria.setCityCode(staticCriteria.getCityCode());
            searchCriteria.setCountryCode(staticCriteria.getCountryCode());
            searchCriteria.setLocationId(staticCriteria.getLocationId());
            searchCriteria.setLocationType(staticCriteria.getLocationType());
            searchCriteria.setCurrency(staticCriteria.getCurrency());
            searchCriteria.setRoomStayCandidates(staticCriteria.getRoomStayCandidates());

            // Map user global info if available
            if (staticCriteria.getUserGlobalInfo() != null) {
                searchCriteria.setUserGlobalInfo(staticCriteria.getUserGlobalInfo());
            }

            // Map multi currency info if available
            if (staticCriteria.getMultiCurrencyInfo() != null) {
                searchCriteria.setMultiCurrencyInfo(staticCriteria.getMultiCurrencyInfo());
            }

            searchHotelsRequest.setSearchCriteria(searchCriteria);
        }

        // Map filter criteria
        if (staticDetailRequest.getFilterCriteria() != null) {
            searchHotelsRequest.setFilterCriteria(staticDetailRequest.getFilterCriteria());
        }

        return searchHotelsRequest;
    }

    private ComparatorResponse buildHotelCompareResponseResponse(StaticDetailRequest staticDetailRequest, SearchHotelsRequest searchHotelsRequest, Map<String, com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse> hotelCompareResponseMap,
                                                                 Map<String, String> expDataMap, boolean isLiteResponse, CommonModifierResponse commonModifierResponse, String rscValue) {
        if (!hotelCompareResponseMap.containsKey("RECOMMENDED_HOTELS")) {
            return null;
        }

        ComparatorResponse comparatorResponse = new ComparatorResponse();
        com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse recommendedHotels = hotelCompareResponseMap.get("RECOMMENDED_HOTELS");
//        comparatorResponse.setCtaMap(buildCTAMap(staticDetailRequest, commonModifierResponse,
//                recommendedHotels, expDataMap, rscValue));

        HotelDetails hotelDetails = Optional.of(recommendedHotels)
                .map(com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse::getPersonalizedSections)
                .map(PersonalizedSectionDetails::getHotels)
                .filter(hotels -> !hotels.isEmpty())
                .map(hotels -> hotels.get(0))
                .orElse(new HotelDetails());
//        comparatorResponse.setDeeplink(buildDetailDeepLink(staticDetailRequest, commonModifierResponse, expDataMap,
//                staticDetailRequest.getSearchCriteria().getHotelId(), hotelDetails.getId(),
//                hotelDetails.isAltAcco(), hotelDetails.getHotelCategory(), hotelDetails.isMaskedPropertyName(), rscValue));
        comparatorResponse.setTitle(recommendedHotels.getSectionTitle());

        if(recommendedHotels != null && recommendedHotels.getShowCta() != null) {
            comparatorResponse.setShowCTA(recommendedHotels.getShowCta());
        }
        if (recommendedHotels.getShowCta() != null && !recommendedHotels.getShowCta()) {
            comparatorResponse.setRepositionIndex(repositionIndex);
            comparatorResponse.setBannerInfo(buildBannerInfo());
        }

        com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse hotelList = hotelCompareResponseMap.get("RECOMMENDED_HOTELS");
        if(hotelList != null) {
            List<Hotel> finalList = getListingHotels(searchHotelsRequest, hotelList, commonModifierResponse, expDataMap, isLiteResponse, false);
            if (CollectionUtils.isNotEmpty(finalList)) {
                comparatorResponse.setHotelList(finalList);
            }
        }

        if (hotelCompareResponseMap.containsKey("SPONSORED_HOTELS")) {
            com.gommt.hotels.orchestrator.detail.model.response.ComparatorResponse sponsoredHotelList = hotelCompareResponseMap.get("SPONSORED_HOTELS");
            List<Hotel> finalList = getListingHotels(searchHotelsRequest, sponsoredHotelList, commonModifierResponse, expDataMap, isLiteResponse, true);
            if (CollectionUtils.isNotEmpty(finalList)) {
                comparatorResponse.setSponsoredIconUrl(sponsoredHotelIconUrl);
                comparatorResponse.setSponsoredHotelList(finalList);
            }
        }

        return comparatorResponse;

    }

    private BannerInfo buildBannerInfo() {
        BannerInfo bannerInfo = new BannerInfo();
        bannerInfo.setText(polyglotService.getTranslatedData(ConstantsTranslation.ABO_BANNER_INFO_TEXT));
        bannerInfo.setBackground("#FFEDD1");
        return bannerInfo;
    }

    private List<com.mmt.hotels.clientgateway.response.CategoryDatum> mapCategoryDataCG(List<com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum> sourceList) {
        if (sourceList == null) return null;
        List<com.mmt.hotels.clientgateway.response.CategoryDatum> targetList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.places.CategoryDatum src : sourceList) {
            com.mmt.hotels.clientgateway.response.CategoryDatum tgt = new com.mmt.hotels.clientgateway.response.CategoryDatum();
            tgt.setPlaceId(src.getPlaceId());
            tgt.setPlaceName(src.getPlaceName());
            tgt.setLocation(mapLocation(src.getLocation()));
            tgt.setCategory(src.getCategory());
            tgt.setDistance(src.getDistance());
            tgt.setDistanceUnit(src.getDistanceUnit());
            tgt.setSeoUrl(src.getSeoUrl());
            tgt.setVoyId(src.getVoyId());
            tgt.setCtaUrl(src.getCtaUrl());
            tgt.setAddress(src.getAddress());
            tgt.setTagLine(src.getTagLine());
            tgt.setPerformanceTags(src.getPerformanceTags());
            tgt.setImageList(mapPoiImageList(src.getImageList()));
            targetList.add(tgt);
        }
        return targetList;
    }

    public com.mmt.hotels.clientgateway.response.PlacesResponseCG mapToPlacesResponseCG(PlacesResponse placesResponse, Map<String, String> expDataMap) {
        com.mmt.hotels.clientgateway.response.PlacesResponseCG placesResponseCG = new com.mmt.hotels.clientgateway.response.PlacesResponseCG();
        if (placesResponse != null) {
            placesResponseCG.setCardType(placesResponse.getCardType());
            placesResponseCG.setCategories(mapCategoriesCG(placesResponse.getCategories()));
            placesResponseCG.setDirectionsToReach(mapDirectionDetailsCG(placesResponse.getDirectionsToReach()));

            if (utility.isExperimentTrue(expDataMap, LOCATION_SECTION_RATING.getKey()))
                placesResponseCG.setLocRatingData(buildRatingDataCG(placesResponse.getRatingData()));
            return placesResponseCG;
        }
        return null;
    }

    private List<com.mmt.hotels.clientgateway.response.Category> mapCategoriesCG(List<com.gommt.hotels.orchestrator.detail.model.response.content.places.Category> sourceList) {
        if (CollectionUtils.isEmpty(sourceList))
            return null;
        List<com.mmt.hotels.clientgateway.response.Category> targetList = new ArrayList<>();
        for (com.gommt.hotels.orchestrator.detail.model.response.content.places.Category src : sourceList) {
            com.mmt.hotels.clientgateway.response.Category tgt = new com.mmt.hotels.clientgateway.response.Category();
            tgt.setPriority(src.getPriority());
            tgt.setIconUrl(src.getIconUrl());
            tgt.setCategoryType(src.getCategoryType());
            targetList.add(tgt);
            tgt.setCategoryData(mapCategoryDataCG(src.getCategoryData()));
        }
        return targetList;
    }

    private com.mmt.hotels.clientgateway.response.DirectionDetails mapDirectionDetailsCG(com.gommt.hotels.orchestrator.detail.model.response.content.places.DirectionDetails source) {
        if (source == null) return null;
        com.mmt.hotels.clientgateway.response.DirectionDetails target = new com.mmt.hotels.clientgateway.response.DirectionDetails();
        target.setAddressDetail(source.getAddressDetail());
        target.setCtaText(source.getCtaText());
        return target;
    }

    private com.mmt.hotels.clientgateway.response.UGCRatingDataCG buildRatingDataCG(com.gommt.hotels.orchestrator.detail.model.response.ugc.RatingData ratingData) {
        if (ratingData == null) return null;

        com.mmt.hotels.clientgateway.response.UGCRatingDataCG ugcRatingDataCG = new com.mmt.hotels.clientgateway.response.UGCRatingDataCG();

        // Map basic fields from regular buildRatingData pattern
        if (ratingData.getTitle() != null) {
            ugcRatingDataCG.setTitle(ratingData.getTitle());
        }
        if (ratingData.getSubTitle() != null) {
            ugcRatingDataCG.setSubTitle(ratingData.getSubTitle());
        }
        ugcRatingDataCG.setShowIcon(ratingData.isShowIcon());
        ugcRatingDataCG.setIconUrl(giaIconURl);

        // Map summary
        if (ratingData.getSummary() != null) {
            com.mmt.hotels.clientgateway.response.RatingDetail summaryDetail = new com.mmt.hotels.clientgateway.response.RatingDetail();
            summaryDetail.setText(ratingData.getSummary().getText());
            summaryDetail.setIconUrl(ratingData.getSummary().getIconUrl());
            ugcRatingDataCG.setSummary(summaryDetail);
        }

        if (CollectionUtils.isNotEmpty(ratingData.getHighlights())) {
            List<com.mmt.hotels.clientgateway.response.RatingDetail> highlights = new ArrayList<>();
            for (com.gommt.hotels.orchestrator.detail.model.response.common.DisplayItem highlight : ratingData.getHighlights()) {
                com.mmt.hotels.clientgateway.response.RatingDetail highlightDetail = new com.mmt.hotels.clientgateway.response.RatingDetail();
                highlightDetail.setText(highlight.getText());
                if (highlight.getIconUrl() != null) {
                    highlightDetail.setIconUrl(highlight.getIconUrl());
                }
                highlights.add(highlightDetail);
            }
            ugcRatingDataCG.setHighlights(highlights);
        }

        if (MapUtils.isNotEmpty(ratingData.getPersuasionMap())) {
            List<com.mmt.hotels.clientgateway.response.LocationPersuasions> persuasionList = new ArrayList<>();

            Map<String, com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData> persuasionMap =
                    ratingData.getPersuasionMap();

            if (persuasionMap.containsKey("UGC_LOCATION")) {
                com.gommt.hotels.orchestrator.detail.model.response.peithos.transformedResponse.HotelPersuasionData ugcWashroomData =
                        persuasionMap.get("UGC_LOCATION");

                if (ugcWashroomData != null && CollectionUtils.isNotEmpty(ugcWashroomData.getData())) {
                    ugcWashroomData.getData().stream()
                            .filter(Objects::nonNull)
                            .filter(persuasionValue -> StringUtils.isNotEmpty(persuasionValue.getText()))
                            .forEach(persuasionValue -> {
                                com.mmt.hotels.clientgateway.response.LocationPersuasions persuasions = new com.mmt.hotels.clientgateway.response.LocationPersuasions();
                                persuasions.setText(persuasionValue.getText());
                                persuasions.setPriority(String.valueOf(persuasionValue.getPriority()));
                                persuasionList.add(persuasions);
                            });
                }
            }

            if (CollectionUtils.isNotEmpty(persuasionList) && persuasionList.size() > 1) {
                ugcRatingDataCG.setPersuasionList(persuasionList);
            }
        }

        return ugcRatingDataCG;
    }

} 