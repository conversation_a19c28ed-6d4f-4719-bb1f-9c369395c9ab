package com.mmt.hotels.clientgateway.transformer.request.ios;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersRequest;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.transformer.request.SmartFiltersRequestTransformer;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class SmartFiltersRequestTransformerIOS extends SmartFiltersRequestTransformer {

    @Override
    protected void applyClientSpecificTransformations(
            SmartFiltersRequest smartFiltersRequest, 
            FilterCountRequest filterCountRequest, 
            List<String> filterTitles) {

        // iOS-specific transformations can be added here
        // For now, just use the default implementation
    }
}
