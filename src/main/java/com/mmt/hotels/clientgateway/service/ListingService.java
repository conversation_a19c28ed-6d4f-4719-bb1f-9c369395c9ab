package com.mmt.hotels.clientgateway.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.businessobjects.FilterConfiguration;
import com.mmt.hotels.clientgateway.businessobjects.FilterDetail;
import com.mmt.hotels.clientgateway.businessobjects.FilterPillConfigurationWrapper;
import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersRequest;
import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.enums.*;
import com.mmt.hotels.clientgateway.exception.AuthenticationException;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.util.ExceptionHandlerResponse;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.helpers.FetchCollectionHelper;
import com.mmt.hotels.clientgateway.helpers.ListingHelper;
import com.mmt.hotels.clientgateway.pms.CommonConfig;
import com.mmt.hotels.clientgateway.pms.MobConfigHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.response.GroupBookingResponse;
import com.mmt.hotels.clientgateway.response.filter.FilterCategory;
import com.mmt.hotels.clientgateway.response.filter.FilterGroup;
import com.mmt.hotels.clientgateway.response.filter.FilterResponse;
import com.mmt.hotels.clientgateway.response.listingmap.ListingMapResponse;
import com.mmt.hotels.clientgateway.response.moblanding.MobLandingResponse;
import com.mmt.hotels.clientgateway.response.searchHotels.*;
import com.mmt.hotels.clientgateway.restexecutors.FilterExecutor;
import com.mmt.hotels.clientgateway.restexecutors.ListingMapExecutor;
import com.mmt.hotels.clientgateway.restexecutors.MobLandingExecutor;
import com.mmt.hotels.clientgateway.restexecutors.SearchHotelsExecutor;
import com.mmt.hotels.clientgateway.transformer.factory.*;
import com.mmt.hotels.clientgateway.transformer.factory.SmartFiltersFactory;
import com.mmt.hotels.clientgateway.transformer.request.OldToNewerRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.SmartFiltersRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.response.FilterResponseTransformer;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.filter.Filter;
import com.mmt.hotels.kafka.JsonKafkaProducer;
import com.mmt.hotels.model.request.FilterSearchMetaDataResponse;
import com.mmt.hotels.model.request.SearchWrapperInputRequest;
import com.mmt.hotels.model.response.altaccodata.CardPayloadResponse;
import com.mmt.hotels.model.response.athena.Card;
import com.mmt.hotels.model.response.athena.TrendingNowCard;
import com.mmt.hotels.model.response.athena.TrendingNowData;
import com.mmt.hotels.model.response.errors.Error;
import com.mmt.hotels.model.response.errors.ResponseErrors;
import com.mmt.hotels.model.response.listpersonalization.GenericCardPayloadData;
import com.mmt.hotels.model.response.searchwrapper.*;
import com.mmt.hotels.pojo.listing.personalization.CardAction;
import com.mmt.hotels.pojo.listing.personalization.CardData;
import com.mmt.hotels.pojo.request.landing.HotelLandingMobRequestBody;
import com.mmt.hotels.pojo.response.HotelListingMapResponse;
import com.mmt.hotels.pojo.response.landing.HotelLandingWrapperResponse;
import com.mmt.propertymanager.config.PropertyManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.checkerframework.checker.units.qual.C;
import org.codehaus.plexus.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Type;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Future;

import static com.mmt.hotels.clientgateway.constants.Constants.*;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_FILTER_COUNT;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;

@Component
public class ListingService {

    @Autowired
    private MobLandingFactory mobLandingFactory;

    @Autowired
    private MobLandingExecutor mobLandingExecutor;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    @Autowired
    private CommonHelper commonHelper;

    @Autowired
    private ListingHelper listingHelper;

    @Autowired
    private SearchHotelsExecutor searchHotelsExecutor;

    @Autowired
    private FilterExecutor filterExecutor;

    @Autowired
    private FilterFactory filterFactory;

    @Autowired
    private ListingMapFactory listingMapFactory;

    @Autowired
    private ListingMapExecutor listingMapExecutor;

    @Autowired
    private MobConfigHelper mobConfigHelper;

    @Autowired
    private OldToNewerRequestTransformer oldToNewerRequestTransformer;

    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private HotelMetaDataService hotelMetaDataService;

    @Autowired
    private ObjectMapperUtil objectMapperUtil;

    @Autowired
    private MetricAspect metricAspect;

    @Autowired
    private FilterResponseTransformer filterResponseTransformer;

    @Autowired
    PolyglotService polyglotService;

    @Autowired
    private Utility utility;

    @Autowired
    private FetchCollectionHelper fetchCollectionHelper;

    @Autowired
    private SmartFiltersFactory smartFiltersFactory;

    @Autowired
    private PropertyManager propManager;

    @Autowired
    private OrchListingService orchListingService;

    @Autowired
    private FilterPillFactory filterPillFactory;

    private Map<String, FilterDetail> landingFilterConditions;

    FilterPillConfigurationWrapper filterPillConfigurationWrapper;

//    @Autowired
//    @Qualifier("jsonKafkaProducer")
    JsonKafkaProducer<GroupBookingRequest> jsonKafkaProducer;

    @Value("${group.booking.kafka.topic:groupBookingTopic}")
    private String groupBookingKafkaTopic;

    @Value("${group.booking.success.msg:Your request has been submitted successfully}")
    private String groupBookingSuccessMsg;

    private static final Logger logger = LoggerFactory.getLogger(ListingService.class);

    @PostConstruct
    public void init() {
        CommonConfig commonConfig = propManager.getProperty("commonConfig", CommonConfig.class);
        landingFilterConditions = commonConfig.landingFilterConditions();
        commonConfig.addPropertyChangeListener("landingFilterConditions", event -> landingFilterConditions = commonConfig.landingFilterConditions());
    }

    /*
     * This is old searchHotels just for the sake of maintaining backward compatibility
     * of older apps.
     */
    public String searchHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
    	try {

    		SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
    		CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            searchHotelsRequest.setExpDataMap(commonModifierResponse.getExpDataMap());
            if (utility.isRearchFlow(searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getRequestId() : "", searchHotelsRequest.getExpDataMap())) {
                return orchListingService.searchHotelsScion(searchHotelsRequest, parameterMap, httpHeaderMap, commonModifierResponse);
            }

            return searchHotelsExecutor.searchHotelsOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        } catch (Throwable e) {
    	    if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in searchHotelsOld: " + e.getMessage());
                logger.debug("error occurred in searchHotelsOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in searchHotelsOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String landingDiscoveryOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.landingDiscoveryOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in landingDiscoveryOld: " + e.getMessage());
                logger.debug("error occurred in landingDiscoveryOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in landingDiscoveryOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String searchPersonalizedHotelsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
    	try {

    		SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
    		CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
        	//searchHotelsRequest.setSortCriteria(listingHelper.getSortCriteria(searchHotelsRequest, commonModifierResponse, searchHotelsRequest.getSearchCriteria()));
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.searchPersonalizedHotelsOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
        	else if (e instanceof ErrorResponseFromDownstreamException) {
        		logger.error("error occurred in searchPersonalizedHotelsOld: " + e.getMessage());
        		logger.debug("error occurred in searchPersonalizedHotelsOld: " + e.getMessage(), e);
        	}else
        		logger.error("error occurred in searchPersonalizedHotelsOld: " + e.getMessage(), e);

        	ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String listingMapOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.listingMapOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in listingMapOld: " + e.getMessage());
                logger.debug("error occurred in listingMapOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in listingMapOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public SearchHotelsResponse searchHotels(SearchHotelsRequest searchHotelsRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);

            if (utility.isRearchFlow(searchHotelsRequest.getRequestDetails() != null ? searchHotelsRequest.getRequestDetails().getRequestId() : "", searchHotelsRequest.getExpDataMap())) {
                return orchListingService.searchHotels(searchHotelsRequest, commonModifierResponse, parameterMap, httpHeaderMap);
            }

            if (searchHotelsRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(searchHotelsRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getSearchCriteria().getCheckIn(),
                        searchHotelsRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);
            SearchWrapperInputRequest searchWrapperInputRequest = searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                    .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            ListingPagePersonalizationResponsBO listingPagePersonalizationResponseBO = null;
            if (searchHotelsRequest.getSearchCriteria().isNearBySearch()) {
                SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = searchHotelsExecutor.nearByHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
                listingPagePersonalizationResponseBO = listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO,searchHotelsRequest);
            } else if (searchHotelsRequest.getSearchCriteria().isPersonalizedSearch()) {
                listingPagePersonalizationResponseBO = searchHotelsExecutor.searchPersonalizedHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
            } else {
                SearchWrapperResponseBO<SearchWrapperHotelEntity> searchWrapperResponseBO = searchHotelsExecutor.searchHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
                listingPagePersonalizationResponseBO = listingHelper.convertSearchHotelsToPersonalizedHotels(searchWrapperResponseBO,searchHotelsRequest);
            }
            return searchHotelsFactory.getResponseService(searchHotelsRequest.getClient())
                    .convertSearchHotelsResponse(listingPagePersonalizationResponseBO,searchHotelsRequest,commonModifierResponse);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in searchHotels: " + e.getMessage());
                logger.debug("error occurred in searchHotels: " + e.getMessage(), e);
            } else
                logger.error("error occurred in searchHotels: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public MobLandingResponse mobLanding(MobLandingRequest mobLandingRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {
            commonHelper.updateCurrencyAndSource(mobLandingRequest.getSearchCriteria(), mobLandingRequest.getRequestDetails(), httpHeaderMap);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(mobLandingRequest.getSearchCriteria(), mobLandingRequest, httpHeaderMap);
            HotelLandingMobRequestBody hotelLandingMobRequestBody =
                    mobLandingFactory.getRequestService(mobLandingRequest.getClient())
                            .convertMobLandingRequest(mobLandingRequest, commonModifierResponse);
            Future<String> response = mobLandingExecutor.moblanding(hotelLandingMobRequestBody, parameterMap, httpHeaderMap);
            Future<Object> pageMakerOfferResponse = null;
            Future<Object> pageMakerRenderedTemplateResponse = null;
            Future<Object> smartEngageResponse = null;
            String deviceType = mobLandingRequest.getDeviceDetails().getBookingDevice();
            String flavour = commonHelper.getFlavour(deviceType);
            if (commonModifierResponse != null && utility.isBusinessIdentifyEnableExperimentOn(commonModifierResponse.getExpDataMap())
                    && commonModifierResponse.getExtendedUser() != null
                    && !utility.isBusinessIdentificationEnableFromUserService(commonModifierResponse.getExtendedUser())
                    && commonHelper.isBusinessUserApplicableFromFeatureStore(commonModifierResponse.getExtendedUser())) {
                    commonModifierResponse.getExpDataMap().put(Constants.SHOW_BUSINESS_IDENTIFICATION_CARD_EXP, Constants.TRUE);
            }
            String version = mobLandingRequest.getDeviceDetails().getAppVersion();
            String funnelSource = mobLandingRequest.getRequestDetails().getFunnelSource();
            String siteDomain = mobLandingRequest.getRequestDetails().getSiteDomain();
            String pageContext = mobLandingRequest.getRequestDetails().getPageContext();
            if(mobLandingRequest.getRequiredApis() != null){
                if(mobLandingRequest.getRequiredApis().isPagemakerRequired()){
                    pageMakerOfferResponse =  mobLandingExecutor.getPageMakerChunkData(funnelSource,flavour,version, httpHeaderMap);;
                    if(FUNNEL_DAYUSE.equalsIgnoreCase(mobLandingRequest.getRequestDetails().getFunnelSource())){
                        pageMakerRenderedTemplateResponse =  mobLandingExecutor.getPageMakerRenderedData(flavour,version, httpHeaderMap);
                    }
                }
                if(mobLandingRequest.getRequiredApis().isSmartEngageRequired()){
                    Map<String, Object> map = createParamsMap(mobLandingRequest, pageContext);
                    smartEngageResponse = mobLandingExecutor.getSmartAds(flavour,version,siteDomain,deviceType, map, httpHeaderMap, pageContext);
                }
            }
            HotelLandingWrapperResponse hotelLandingWrapperResponse = objectMapperUtil.getObjectFromJson(response.get(), HotelLandingWrapperResponse.class, DependencyLayer.ORCHESTRATOR);
            if (hotelLandingWrapperResponse != null && hotelLandingWrapperResponse.getErrorResponse() != null
                    && CollectionUtils.isNotEmpty(hotelLandingWrapperResponse.getErrorResponse().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, hotelLandingWrapperResponse.getErrorResponse().getErrorList().get(0).getErrorCode(),
                        hotelLandingWrapperResponse.getErrorResponse().getErrorList().get(0).getErrorMessage());
            }
            return mobLandingFactory.getResponseService(mobLandingRequest.getClient())
                    .convertMobLandingResponse(hotelLandingWrapperResponse, mobLandingRequest.getClient(), commonModifierResponse.getExpDataMap(),pageMakerOfferResponse!= null ? pageMakerOfferResponse.get() : null, smartEngageResponse != null ? smartEngageResponse.get() : null,pageMakerRenderedTemplateResponse != null ? pageMakerRenderedTemplateResponse.get() : null, commonModifierResponse,mobLandingRequest);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in mobLanding: " + e.getMessage());
                logger.debug("error occurred in mobLanding: " + e.getMessage(), e);
            } else
                logger.error("error occurred in mobLanding: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String mobLandingOld(HotelLandingMobRequestBody mobLandingBody, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {
            MobLandingRequest mobLandingRequest = oldToNewerRequestTransformer.updateMobLandingRequest(mobLandingBody);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(mobLandingRequest.getSearchCriteria(), mobLandingRequest, httpHeaderMap);
            commonHelper.updateCurrencyAndSource(mobLandingRequest.getSearchCriteria(), mobLandingRequest.getRequestDetails(), httpHeaderMap);
            HotelLandingMobRequestBody hotelLandingMobRequestBody =
                    mobLandingFactory.getRequestService(mobLandingRequest.getClient())
                            .convertMobLandingRequest(mobLandingRequest, commonModifierResponse);
            return  mobLandingExecutor.moblanding(hotelLandingMobRequestBody,parameterMap,httpHeaderMap).get();

        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in mobLanding: " + e.getMessage());
                logger.debug("error occurred in mobLanding: " + e.getMessage(), e);
            } else
                logger.error("error occurred in mobLanding: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    /*
     * This is old searchHotels just for the sake of maintaining backward compatibility
     * of older apps.
     */
    public String filterCountOld(SearchWrapperInputRequest filterRequest, Map<String, String[]> parameterMap, Map<String,String> httpHeaderMap) throws ClientGatewayException {
        try {

            ListingSearchRequest filterRequestNew = oldToNewerRequestTransformer.updateSearchHotelsRequest(filterRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequestNew.getSearchCriteria(), filterRequestNew, httpHeaderMap);
           // filterRequestNew.setSortCriteria(listingHelper.getSortCriteria(filterRequestNew, commonModifierResponse, filterRequestNew.getSearchCriteria()));
            SearchWrapperInputRequest filterRequestHES =
                    filterFactory.getRequestService(filterRequest.getBookingDevice())
                            .convertSearchRequest(filterRequestNew, commonModifierResponse);
           return filterExecutor.filterCount(filterRequestHES, httpHeaderMap,String.class);

        }catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in filterCountOld: " + e.getMessage());
            }else
                logger.error("error occurred in filterCountOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public FilterResponse filterCount(FilterCountRequest filterRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, Boolean seoCorp) throws ClientGatewayException {
        FilterResponse filterResponse = null;
        try {
            long startTime = System.currentTimeMillis();
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequest.getSearchCriteria(), filterRequest, httpHeaderMap);
            if (seoCorp != null && Boolean.TRUE.equals(seoCorp) && filterRequest.getRequestDetails() != null) {
                filterRequest.getRequestDetails().setSeoCorp(true);
                filterRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
            }
            /*ADDING FILTER HOSTEL FOR HOSTEL FUNNEL*/
            if (Constants.FUNNEL_SOURCE_HOSTEL.equalsIgnoreCase(filterRequest.getRequestDetails().getFunnelSource())) {
                if (CollectionUtils.isEmpty(filterRequest.getFilterCriteria())) {
                    filterRequest.setFilterCriteria(new ArrayList<>());
                }
                com.mmt.hotels.clientgateway.request.Filter hostelFilter = new com.mmt.hotels.clientgateway.request.Filter(FilterGroup.PROPERTY_TYPE, Constants.PROPERTY_TYPE_HOSTEL);
                filterRequest.getFilterCriteria().add(0, hostelFilter);
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_FILTER_REQUEST_PROCESSOR, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();
            SearchWrapperInputRequest filterRequestHES =
                    filterFactory.getRequestService(filterRequest.getClient())
                            .convertSearchRequest(filterRequest, commonModifierResponse);

            FilterSearchMetaDataResponse filterResponseHES = filterExecutor.filterCount(filterRequestHES, httpHeaderMap, FilterSearchMetaDataResponse.class);
            if (filterResponseHES != null && filterResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(filterResponseHES.getResponseErrors().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            } else if (MapUtils.isEmpty(filterResponseHES.getFilterDataMap())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.MARSHALLING, MarshallingErrors.NO_DATA_FOUND.getErrorCode(), MarshallingErrors.NO_DATA_FOUND.getErrorMsg());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_DOWNSTREAM_FILTER_HES_CALL, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();

            /*
             * myPartner change log : commonModifierResponse floated down, check the method comment for more info
             * */
            FilterConfiguration filterConfiguration = filterFactory.getFilterConfiguration(filterRequest.getClient(),
                    filterRequest.getRequestDetails().getIdContext(), filterRequest.getRequestDetails().getFunnelSource(), commonModifierResponse);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_FILTER_PMS_CONFIGURATION, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);
            startTime = System.currentTimeMillis();

            // This is the wrapper that holds config for filter pills
            boolean isFilterPillExperiment = commonModifierResponse != null && MapUtils.isNotEmpty(commonModifierResponse.getExpDataMap()) && Constants.EXP_TRUE_VALUE.equalsIgnoreCase(commonModifierResponse.getExpDataMap().get(Constants.FILTER_PILL_EXP));
            boolean isMyPartnerRequest = (commonModifierResponse!=null) && (commonModifierResponse.getExtendedUser()!=null) && Utility.isMyPartnerRequest(commonModifierResponse.getExtendedUser().getProfileType(), commonModifierResponse.getExtendedUser().getAffiliateId());
            String country= null!=filterRequest && "IN".equalsIgnoreCase(filterRequest.getSearchCriteria().getCountryCode())? "DOM":"INTL";

            if (isFilterPillExperiment && !isMyPartnerRequest && (filterRequest.getRequestDetails() != null) && (filterRequest.getSearchCriteria() != null)) {
                filterPillConfigurationWrapper = filterPillFactory.getFilterPillConfiguration(filterRequest.getRequestDetails().getFunnelSource(),
                        filterRequest.getSearchCriteria().getLocationType(), country.equalsIgnoreCase("DOM"),filterResponseHES.getAccessPoints(),commonModifierResponse.getExpDataMap());
            }
            filterResponse = filterFactory.getResponseService(filterRequest.getClient())
                    .convertFilterResponse(filterResponseHES, filterConfiguration, filterRequest, commonModifierResponse.getExpDataMap(), commonModifierResponse, filterPillConfigurationWrapper);
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_FILTER_RESPONSE_PROCESS, LISTING_FILTER_COUNT, System.currentTimeMillis() - startTime);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in filterCount: " + e.getMessage());
                logger.debug("error occurred in filterCount: " + e.getMessage(), e);
            } else
                logger.error("error occurred in filterCount: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return filterResponse;
    }

    public FilterResponse batchFilterResponse(FilterCountRequest filterRequest, Map<String, String> httpHeaderMap, Boolean seoCorp) throws ClientGatewayException {
        FilterResponse batchFilterResponse = null;
        try {
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(filterRequest.getSearchCriteria(), filterRequest, httpHeaderMap);
            if (Boolean.TRUE.equals(seoCorp) && filterRequest.getRequestDetails() != null) {
                filterRequest.getRequestDetails().setSeoCorp(true);
                filterRequest.getRequestDetails().setIdContext(Constants.CORP_ID_CONTEXT);
            }
            SearchWrapperInputRequest filterRequestHES = filterFactory.getRequestService(filterRequest.getClient())
                            .convertSearchRequest(filterRequest, commonModifierResponse);
            filterRequestHES.setBatchFiltersRequest(true);
            FilterSearchMetaDataResponse filterResponseHES = filterExecutor.filterCount(filterRequestHES, httpHeaderMap, FilterSearchMetaDataResponse.class);
            if (filterResponseHES != null && filterResponseHES.getResponseErrors() != null && CollectionUtils.isNotEmpty(filterResponseHES.getResponseErrors().getErrorList())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.DOWNSTREAM, filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorCode(),
                        filterResponseHES.getResponseErrors().getErrorList().get(0).getErrorMessage());
            } else if (filterResponseHES != null && CollectionUtils.isEmpty(filterResponseHES.getBatchFilters())) {
                throw new ErrorResponseFromDownstreamException(DependencyLayer.ORCHESTRATOR, ErrorType.MARSHALLING, MarshallingErrors.NO_DATA_FOUND.getErrorCode(), MarshallingErrors.NO_DATA_FOUND.getErrorMsg());
            }

            batchFilterResponse = filterFactory.getResponseService(filterRequest.getClient()).convertBatchFilterResponse(filterResponseHES);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in batchFilterResponse: " + e.getMessage());
                logger.debug("error occurred in batchFilterResponse: " + e.getMessage(), e);
            } else
                logger.error("error occurred in batchFilterResponse: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
        return batchFilterResponse;
    }

    public ListingMapResponse listingMap(ListingMapRequest listingMapRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {

        try {
        	CommonModifierResponse commonModifierResponse = commonHelper.processRequest(listingMapRequest.getSearchCriteria(), listingMapRequest, httpHeaderMap);

            SearchWrapperInputRequest listingMapRequestCB =
                    listingMapFactory.getRequestService(listingMapRequest.getClient())
                            .convertListingMapRequest(listingMapRequest, commonModifierResponse);

            HotelListingMapResponse hotelListingMapResponse = listingMapExecutor.listingMap(listingMapRequestCB, httpHeaderMap);

            return listingMapFactory.getResponseService(listingMapRequest.getClient())
                    .convertListingMapResponse(hotelListingMapResponse,listingMapRequest.getExpData(),listingMapRequest.getDeviceDetails(), commonModifierResponse);
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in listingMap: " + e.getMessage());
                logger.debug("error occurred in listingMap: " + e.getMessage(), e);
            } else
                logger.error("error occurred in listingMap: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String fetchCollectionsOld(SearchWrapperInputRequest searchWrapperInputRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(searchWrapperInputRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
            listingHelper.updateCollectionCounts(searchHotelsRequest);
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            String responseHes = searchHotelsExecutor.fetchCollectionsOld(searchWrapperInputRequestModified,parameterMap,httpHeaderMap);
            return convertHesCollectionResponseToOldFormat(responseHes);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetch collections: " + e.getMessage());
                logger.debug("error occurred in fetch collections: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetch collections: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String convertHesCollectionResponseToOldFormat(String responseHes) {
        try{
            CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = objectMapperUtil.getObjectFromJsonWithType
                    (responseHes, new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {}, DependencyLayer.ORCHESTRATOR);
            if(collectionsResponseBO!=null &&  CollectionUtils.isNotEmpty(collectionsResponseBO.getCardCollections())){
                TrendingNowData trendingNowData=new TrendingNowData();
                CardCollections cardCollection=collectionsResponseBO.getCardCollections().get(0);
                trendingNowData.setTrendingNowCards(new ArrayList<>());
                BeanUtils.copyProperties(cardCollection,trendingNowData);
                cardCollection.getCardList().forEach(card->{
                    TrendingNowCard trendingNowCard=new TrendingNowCard();
                    BeanUtils.copyProperties(card,trendingNowCard);
                    trendingNowData.getTrendingNowCards().add(trendingNowCard);
                });

                collectionsResponseBO.setTrendingNowData(trendingNowData);
                collectionsResponseBO.setCardCollections(null);
            }
            return objectMapperUtil.getJsonFromObject(collectionsResponseBO, DependencyLayer.CLIENTGATEWAY);
        }catch(Exception e){
            logger.error("Exception occurred while converting hes response to old format " + e.getMessage(),e);
        }
        return responseHes;
    }

    public FetchCollectionResponse fetchCollections(FetchCollectionRequest fetchCollectionRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            String requestSource = httpHeaderMap.get(Constants.REQUEST_SOURCE_KEY);
            String responseHes = fetchCollectionOld(fetchCollectionRequest, parameterMap, httpHeaderMap);
            return convertHesCollectionResponse(responseHes,fetchCollectionRequest, requestSource);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetchCollections: " + e.getMessage());
                logger.debug("error occurred in fetchCollections: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetchCollections: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private String fetchCollectionOld(FetchCollectionRequest fetchCollectionRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        CommonModifierResponse commonModifierResponse = commonHelper.processRequest(fetchCollectionRequest.getSearchCriteria(), fetchCollectionRequest, httpHeaderMap);
        listingHelper.updateCollectionCounts(fetchCollectionRequest);
if (fetchCollectionRequest.getSearchCriteria() != null) {
                utility.setLoggingParametersToMDC(fetchCollectionRequest.getSearchCriteria().getRoomStayCandidates(), fetchCollectionRequest.getSearchCriteria().getCheckIn(),
                        fetchCollectionRequest.getSearchCriteria().getCheckOut());
            }        SearchWrapperInputRequest searchWrapperInputRequestModified =
                searchHotelsFactory.getRequestService(fetchCollectionRequest.getClient())
                        .convertSearchRequest(fetchCollectionRequest, commonModifierResponse);

        return searchHotelsExecutor.fetchCollectionsOld(searchWrapperInputRequestModified, parameterMap, httpHeaderMap);
    }

    public FetchCollectionResponseV2 fetchCollectionsV2(FetchCollectionRequest fetchCollectionRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            String requestSource = httpHeaderMap.get(Constants.REQUEST_SOURCE_KEY);
            String responseHes = fetchCollectionOld(fetchCollectionRequest, parameterMap, httpHeaderMap);
            return convertHesCollectionResponseV2(responseHes, fetchCollectionRequest, requestSource);

        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in fetchCollectionsV2: " + e.getMessage());
                logger.debug("error occurred in fetchCollectionsV2: " + e.getMessage(), e);
            } else
                logger.error("error occurred in fetchCollectionsV2: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    private FetchCollectionResponseV2 convertHesCollectionResponseV2(String responseHes, FetchCollectionRequest fetchCollectionRequest, String requestSource) {
        FetchCollectionResponseV2 fetchCollectionResponseV2 = new FetchCollectionResponseV2();
        try {
            CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = objectMapperUtil.getObjectFromJsonWithType
                    (responseHes, new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {
                    }, DependencyLayer.ORCHESTRATOR);
            fetchCollectionResponseV2.setCorrelationKey(collectionsResponseBO.getCorrelationKey());
            if (collectionsResponseBO.getResponseErrors() != null) {
                fetchCollectionResponseV2.setResponseErrors(collectionsResponseBO.getResponseErrors());
                return fetchCollectionResponseV2;
            }
            fetchCollectionResponseV2.setFetchCollectionList(getCollectionList(collectionsResponseBO, fetchCollectionRequest, requestSource));
            if (fetchCollectionHelper.shouldAddFilters(Constants.MORE_FILTERS, fetchCollectionRequest, landingFilterConditions)) {
                List<FilterCategory> moreFilters = getMoreFilters(collectionsResponseBO, fetchCollectionRequest);
                fetchCollectionResponseV2.setMoreFilters(moreFilters);
            }
            if (fetchCollectionHelper.shouldAddFilters(Constants.SUGGESTED_FILTERS, fetchCollectionRequest, landingFilterConditions)) {
                if (collectionsResponseBO.getSuggestedFilters() != null && MapUtils.isNotEmpty(collectionsResponseBO.getSuggestedFilters().getPreferredFilters())) {
                    CollectionFilters suggestedFilters = new CollectionFilters();
                    suggestedFilters.setHeading(collectionsResponseBO.getSuggestedFilters().getHeading());
                    suggestedFilters.setSubHeading(collectionsResponseBO.getSuggestedFilters().getSubHeading());
                    List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
                    for (Set<Filter> filterSet : collectionsResponseBO.getSuggestedFilters().getPreferredFilters().values()) {
                        filters.addAll(filterResponseTransformer.buildFilter(new ArrayList<>(filterSet)));
                    }
                    suggestedFilters.setFilters(filters);
                    fetchCollectionResponseV2.setSuggestedFilters(suggestedFilters);
                }
            }
            if (fetchCollectionHelper.shouldAddFilters(Constants.OTHER_FILTER_CATEGORY, fetchCollectionRequest, landingFilterConditions)) {
                FilterCategory otherFilterCategory = getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest);
                fetchCollectionResponseV2.setOtherFilterCategory(otherFilterCategory);
            }
            if (fetchCollectionResponseV2 == null) {
                List<Error> errors = new ArrayList<>();
                errors.add(new Error.Builder().buildErrorCode(CBError.NO_COLLECTION_DATA_FROM_CG.getCode(), CBError.NO_COLLECTION_DATA_FROM_CG.getDescription()).build());
                fetchCollectionResponseV2.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
            } else {
                listingHelper.sortBasedOnPriority(fetchCollectionResponseV2.getFetchCollectionList());
            }
        } catch (Exception e) {
            logger.error("Exception occurred while converting HES Response String to object :  " + e.getMessage(), e);
            List<Error> errors = new ArrayList<>();
            errors.add(new Error.Builder().buildErrorCode(CBError.GENERIC_ERROR.getCode(), CBError.GENERIC_ERROR.getDescription()).build());
            fetchCollectionResponseV2.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
        }
        return fetchCollectionResponseV2;
    }

    private FetchCollectionResponse convertHesCollectionResponse(String responseHes, FetchCollectionRequest fetchCollectionRequest, String requestSource) {
        FetchCollectionResponse fetchCollectionResponse = new FetchCollectionResponse();
        try{
            CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO = objectMapperUtil.getObjectFromJsonWithType
                    (responseHes, new TypeReference<CollectionsResponseBo<SearchWrapperHotelEntity>>() {}, DependencyLayer.ORCHESTRATOR);
            fetchCollectionResponse.setCorrelationKey(collectionsResponseBO.getCorrelationKey());
            if(collectionsResponseBO!=null && collectionsResponseBO.getResponseErrors()!=null){
                fetchCollectionResponse.setResponseErrors(collectionsResponseBO.getResponseErrors());
                return fetchCollectionResponse;
            }
            fetchCollectionResponse.setFetchCollectionList(getCollectionList(collectionsResponseBO,fetchCollectionRequest, requestSource));
            FetchCollection suggestedFilters = getSuggestedFilters(collectionsResponseBO);
            fetchCollectionResponse.setSuggestedFilters(suggestedFilters);
            List<FilterCategory> moreFilters = getMoreFilters(collectionsResponseBO, fetchCollectionRequest);
            fetchCollectionResponse.setMoreFilters(moreFilters);

            if(CollectionUtils.isEmpty(fetchCollectionResponse.getFetchCollectionList()) && CollectionUtils.isEmpty(fetchCollectionResponse.getMoreFilters()) &&
                    fetchCollectionRequest.getRequestDetails() != null && fetchCollectionRequest.getRequestDetails().getPageContext() != null &&
                    !fetchCollectionRequest.getRequestDetails().getPageContext().equalsIgnoreCase(Constants.PAGE_CONTEXT_LISTING)){
                List<Error> errors = new ArrayList<>();
                errors.add(new Error.Builder().buildErrorCode(CBError.NO_COLLECTION_DATA_FROM_CG.getCode(),CBError.NO_COLLECTION_DATA_FROM_CG.getDescription()).build());
                fetchCollectionResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
            }else{
                listingHelper.sortBasedOnPriority(fetchCollectionResponse.getFetchCollectionList());
            }
        }catch(Exception e){
            logger.error("Exception occurred while converting HES Response String to object :  " +e.getMessage(),e);
            List<Error> errors = new ArrayList<>();
            errors.add(new Error.Builder().buildErrorCode(CBError.GENERIC_ERROR.getCode(),CBError.GENERIC_ERROR.getDescription()).build());
            fetchCollectionResponse.setResponseErrors(new ResponseErrors.Builder().buildErrorList(errors).build());
        }
        return fetchCollectionResponse;
    }

    private List<FilterCategory> getMoreFilters(CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO, FetchCollectionRequest fetchCollectionRequest) {
        if(MapUtils.isEmpty(collectionsResponseBO.getMoreFiltersMap())) {
            return null;
        }
        Map<String, List<Filter>> moreFiltersMap = collectionsResponseBO.getMoreFiltersMap();
        List<FilterCategory> moreFilters = new ArrayList<>();
        moreFiltersMap.forEach((cardTitle, filterList) -> {
            FilterCategory filterCategory = new FilterCategory();
            filterCategory.setTitle(cardTitle);
            filterCategory.setViewType(filterList.get(0).getViewType());
            filterCategory.setCategoryName(filterList.get(0).getCategoryName());
            filterCategory.setVisible(true);
            filterCategory.setFilters(filterResponseTransformer.buildFilter(filterList));
            moreFilters.add(filterCategory);
        });
        moreFilters.add(getFilterCategoryFromHistoGramBuckets(fetchCollectionRequest));
        return moreFilters;
    }

    public FilterCategory getFilterCategoryFromHistoGramBuckets(FetchCollectionRequest fetchCollectionRequest) {
        FilterCategory hotelPriceFilterCategory = new FilterCategory();
        if(utility.isExpPdoPrnt(fetchCollectionRequest.getExpData())){
            hotelPriceFilterCategory.setTitle(polyglotService.getTranslatedData(Constants.PRICE_PER_ROOM_PER_NIGHT));
        }else{
            hotelPriceFilterCategory.setTitle(polyglotService.getTranslatedData(Constants.PRICE_PER_NIGHT));
        }
        hotelPriceFilterCategory.setViewType(Constants.VIEW_TYPE_GRAPH);
        hotelPriceFilterCategory.setVisible(true);
        hotelPriceFilterCategory.setCategoryName(Constants.PRICE_CATEGORY_NAME);
        hotelPriceFilterCategory.setFilters(filterResponseTransformer.createDefaultPriceHistogramBuckets(StringUtils.isNotBlank(fetchCollectionRequest.getSearchCriteria().getCurrency()) ?
                fetchCollectionRequest.getSearchCriteria().getCurrency().toUpperCase() : fetchCollectionRequest.getSearchCriteria().getCurrency()));
        return hotelPriceFilterCategory;
    }

    private List<FetchCollection> getCollectionList(CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO, FetchCollectionRequest fetchCollectionRequest, String requestSource) {
        List<FetchCollection> fetchCollectionList=new ArrayList<>();
        addFeaturedCollectionsToList(collectionsResponseBO.getCollectionsResponse(),fetchCollectionRequest,fetchCollectionList, requestSource);
        addCardCollectionsToList(collectionsResponseBO.getCardCollections(),fetchCollectionList);

        return fetchCollectionList;
    }

    private FetchCollection getSuggestedFilters(CollectionsResponseBo<SearchWrapperHotelEntity> collectionsResponseBO){

        if (collectionsResponseBO.getSuggestedFilters()==null){
            return null;
        }

        CardCollections suggestedFilter = collectionsResponseBO.getSuggestedFilters();

        FetchCollection fetchCollection=new FetchCollection();
        fetchCollection.setCardType(suggestedFilter.getCardType());
        fetchCollection.setHeading(suggestedFilter.getHeading());
        fetchCollection.setSubHeading(suggestedFilter.getSubHeading());
        fetchCollection.setAppliedFilterMap(suggestedFilter.getPreferredFilters());
        return fetchCollection;
    }

    private void addCardCollectionsToList(List<CardCollections> cardCollectionList, List<FetchCollection> fetchCollectionList) {
        if(CollectionUtils.isNotEmpty(cardCollectionList)){
            cardCollectionList.forEach(cardCollection ->{
                FetchCollection fetchCollection=new FetchCollection();
                fetchCollection.setCardType(cardCollection.getCardType());
                fetchCollection.setHeading(cardCollection.getHeading());
                fetchCollection.setSubHeading(cardCollection.getSubHeading());
                if(CollectionUtils.isNotEmpty(cardCollection.getCardList())){
                    List<CardCG> cardCGS = new ArrayList<>();
                    for(Card card : cardCollection.getCardList()){
                        CardCG cardCG = new CardCG();
                        BeanUtils.copyProperties(card, cardCG);
                        cardCGS.add(cardCG);
                    }
                    // fetchCollection.setCardList(cardCollection.getCardList());
                    fetchCollection.setCardList(cardCGS);
                }
                if(cardCollection!=null && cardCollection.getCardInfo()!=null){
                    fetchCollection.setCardInfo(getCardInfo(cardCollection));
                }
                fetchCollection.setPriority(cardCollection.getPriority());
                fetchCollection.setTemplate(cardCollection.getTemplate());
                fetchCollection.setBgImageUrl(cardCollection.getBgImageUrl());
                fetchCollection.setCollectionDescription(cardCollection.getCollectionDescription());
                fetchCollection.setCta(cardCollection.getCta());
                fetchCollectionList.add(fetchCollection);
            });
        }

    }

    private CardData getCardInfo(CardCollections cardCollection){
        CardData cardInfo = new CardData();
        cardInfo.setSubText(StringUtils.isNotEmpty(cardCollection.getCardInfo().getSubText())?cardCollection.getCardInfo().getSubText():null);
        cardInfo.setTitleText(StringUtils.isNotEmpty(cardCollection.getCardInfo().getTitleText())?cardCollection.getCardInfo().getTitleText():null);
        cardInfo.setIndex(cardCollection.getCardInfo().getIndex());
        cardInfo.setStarText(cardCollection.getCardInfo().getStarText());
        cardInfo.setCardId(StringUtils.isNotEmpty(cardCollection.getCardInfo().getCardId())?cardCollection.getCardInfo().getCardId():null);
        cardInfo.setIconUrl(StringUtils.isNotEmpty(cardCollection.getCardInfo().getIconUrl())?cardCollection.getCardInfo().getIconUrl():null);
        cardInfo.setBgImageUrl(StringUtils.isNotEmpty(cardCollection.getCardInfo().getBgImageUrl())?cardCollection.getCardInfo().getBgImageUrl():null);
        cardInfo.setTemplateId(StringUtils.isNotEmpty(cardCollection.getCardInfo().getTemplateId())?cardCollection.getCardInfo().getTemplateId():null);
        if(CollectionUtils.isNotEmpty(cardCollection.getCardInfo().getCardAction())){
            List<CardAction> cardActionList = new ArrayList<>();
            for(CardAction ca: cardCollection.getCardInfo().getCardAction()){
                CardAction cardAction = new CardAction();
                cardAction.setWebViewUrl(StringUtils.isNotEmpty(ca.getWebViewUrl())?ca.getWebViewUrl():null);
                cardAction.setTitle(StringUtils.isNotEmpty(ca.getTitle())?ca.getTitle():null);
                cardActionList.add(cardAction);
            }
            cardInfo.setCardAction(cardActionList);
        }
        if(cardCollection.getCardInfo().getCardPayload()!=null) {
            cardInfo.setCardPayload(buildCardPayload(cardCollection.getCardInfo().getCardPayload()));
        }
        return cardInfo;
    }

    private CardPayloadResponse buildCardPayload(CardPayloadResponse cardPayloadResponse){
        CardPayloadResponse cardPayloadResponse1 = new CardPayloadResponse();
        List<GenericCardPayloadData> genericCardPayloadDataList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cardPayloadResponse.getGenericCardData()) && CollectionUtils.isNotEmpty(cardPayloadResponse.getGenericCardData().get(0).getData())){
            for (GenericCardPayloadData gpdata: cardPayloadResponse.getGenericCardData().get(0).getData()){
                GenericCardPayloadData genericCardPayloadData = new GenericCardPayloadData();
                genericCardPayloadData.setTitleText(StringUtils.isNotEmpty(gpdata.getTitleText())?gpdata.getTitleText():null);
                genericCardPayloadData.setIconUrl(StringUtils.isNotEmpty(gpdata.getIconUrl())?gpdata.getIconUrl():null);
                genericCardPayloadDataList.add(genericCardPayloadData);
            }
            cardPayloadResponse1.setGenericCardData(genericCardPayloadDataList);
        }
        return cardPayloadResponse1;
    }

    private void addFeaturedCollectionsToList(List<FeaturedCollections<SearchWrapperHotelEntity>> collectionsResponse, FetchCollectionRequest fetchCollectionRequest, List<FetchCollection> fetchCollectionList, String requestSource) {
        if(CollectionUtils.isNotEmpty(collectionsResponse)){
            collectionsResponse.forEach(collectionCard->{
                FetchCollection fetchCollection=new FetchCollection();
                fetchCollection.setCardType("COLLECTION");
                fetchCollection.setHeading(collectionCard.getHeading());
                fetchCollection.setSubHeading(collectionCard.getSubHeading());

                List<CardCG> cardCGS = null;
                if (Constants.AE.equalsIgnoreCase(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue())) && Constants.REQUEST_SOURCE_SCION.equalsIgnoreCase(requestSource)
                        && CollectionUtils.isNotEmpty(collectionCard.getHotels()) && StringUtils.isNotBlank(collectionCard.getCardId())) {
                    fetchCollection.setHotelList(collectionCard.getHotels());
                    fetchCollection.setCardId(collectionCard.getCardId());
                } else {
                    cardCGS = new ArrayList<>();
                    List<Hotel> hotels = searchHotelsFactory.getResponseService(fetchCollectionRequest.getClient()).buildPersonalizedHotels(collectionCard.getHotels(), fetchCollectionRequest.getExpData(), fetchCollectionRequest, null,null);
                    if (CollectionUtils.isNotEmpty(hotels)) {
                        for (Hotel hotel : hotels) {
                            CardCG cardCG = new CardCG();
                            cardCG.setHotel(hotel);
                            cardCGS.add(cardCG);
                        }
                    }
                }

                fetchCollection.setCardList(cardCGS);
                fetchCollection.setPriority(collectionCard.getPriority());
                fetchCollection.setTemplate(collectionCard.getTemplate());
                fetchCollection.setThreshold(collectionCard.getThreshold());
                fetchCollection.setCta(collectionCard.getCta());
                if(null != fetchCollection.getCta())
                    fetchCollection.getCta().setDeeplink(collectionCard.getDeepLink());
                else if(StringUtils.isNotBlank(collectionCard.getDeepLink()))
                    fetchCollection.setCta(new Cta("",collectionCard.getDeepLink()));
                fetchCollectionList.add(fetchCollection);
            });
        }
    }

    public String getMobConfig() throws ClientGatewayException {
        try {
            return mobConfigHelper.getHotelMobConfigAsString();
        } catch (Throwable e) {
            logger.error("error occurred in get mob config: " + e.getMessage(), e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    public String getMetaDataByCityResponse(String cityId, String locationId, String locationType, String filterCode,
                                            String correlationKey, Map<String, String> requestParams) throws ClientGatewayException {

        String srCon = requestParams.get(Constants.SOURCE_CON);
        String destCon = requestParams.get(Constants.DES_CON);
        String srcClient = requestParams.get(Constants.SOURCE_CLIENT);
        String currencyCode = commonHelper.getInboundCurrencyCode(srCon, destCon, srcClient);

        requestParams.put(Constants.LOCATIONID, locationId);
        requestParams.put(Constants.LOCATIONTYPE, locationType);
        requestParams.put(Constants.CURRENCYCODE, currencyCode);
        requestParams.put(Constants.CORRELATIONKEY, correlationKey);

        String response = searchHotelsExecutor.getMetaDataResponse(cityId, requestParams);

        return hotelMetaDataService.filterLocationAndFaclity(response, filterCode);

    }

    public String listingPersonalizationOld(HotelLandingMobRequestBody listPersonalizationRequest, Map<String, String[]> paramMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateSearchHotelsRequest(listPersonalizationRequest.getHotelSearchRequest());
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
          //  searchHotelsRequest.setSortCriteria(listingHelper.getSortCriteria(searchHotelsRequest, commonModifierResponse, searchHotelsRequest.getSearchCriteria()));
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);
            listPersonalizationRequest.setHotelSearchRequest(searchWrapperInputRequestModified);
            return mobLandingExecutor.listPersonalizedCards(listPersonalizationRequest, paramMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in listPersonalizationOld: " + e.getMessage());
                logger.debug("error occurred in listPersonalizationOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in listPersonalizationOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }

    }

    public String nearByOld(SearchWrapperInputRequest nearBySearchRequest, Map<String, String[]> paramMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {

            SearchHotelsRequest searchHotelsRequest = oldToNewerRequestTransformer.updateNearByHotelsRequest(nearBySearchRequest);
            CommonModifierResponse commonModifierResponse = commonHelper.processRequest(searchHotelsRequest.getSearchCriteria(), searchHotelsRequest, httpHeaderMap);
           // searchHotelsRequest.setSortCriteria(listingHelper.getSortCriteria(searchHotelsRequest, commonModifierResponse, searchHotelsRequest.getSearchCriteria()));
            SearchWrapperInputRequest searchWrapperInputRequestModified =
                    searchHotelsFactory.getRequestService(searchHotelsRequest.getClient())
                            .convertSearchRequest(searchHotelsRequest, commonModifierResponse);

            return searchHotelsExecutor.nearByHotelsOld(searchWrapperInputRequestModified, paramMap, httpHeaderMap);
        } catch (Throwable e) {
            if(e instanceof AuthenticationException){
                return ClientBackendUtility.setCBErrorResponse(CBError.UNAUTHORIZED_USER);
            }
            else if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in nearByHotelsOld: " + e.getMessage());
                logger.debug("error occurred in nearByHotelsOld: " + e.getMessage(), e);
            } else
                logger.error("error occurred in nearByHotelsOld: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }


    public GroupBookingResponse submitGroupBooking(GroupBookingRequest groupBookingRequest) throws ClientGatewayException {
//        try {
//            jsonKafkaProducer.sendMessage(groupBookingRequest,groupBookingKafkaTopic);
//        } catch (Exception e) {
//            logger.error("Exception occurred in submitGroupBooking method : " + e.getMessage(), e);
//            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
//            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
//            throw exceptionHandlerResponse.getClientGatewayException();
//        }
        return new GroupBookingResponse();
    }

    public Map<String, Object> createParamsMap(ListingSearchRequest listingSearchRequest, String pageContext) throws ClientGatewayException{
        SearchHotelsCriteria searchCriteria = listingSearchRequest.getSearchCriteria();
        Map<String, Object> paramsMap = new HashMap<>();

        // Extract cityId from searchCriteria and put it into the map
        // Extract the count of adults and children from roomStayCandidates and put them into the map
        int adultCount = 0;
        int childCount = 0;
        String cityId = searchCriteria.getVcId();
        int roomCount = searchCriteria.getRoomStayCandidates().size();
        for (RoomStayCandidate roomStayCandidate : searchCriteria.getRoomStayCandidates()) {
            adultCount += roomStayCandidate.getAdultCount();
            childCount += CollectionUtils.isNotEmpty(roomStayCandidate.getChildAges()) ? roomStayCandidate.getChildAges().size() : 0;
        }
        paramsMap.put("pax_adult", adultCount);
        paramsMap.put("pax_child", childCount);
        if(PAGE_CONTEXT_LISTING.equalsIgnoreCase(pageContext)){
            paramsMap.put("pax_room", roomCount);
            paramsMap.put("city_id", cityId);
        } else{
            paramsMap.put("cityId", cityId);
        }

        // Extract checkIn and checkOut dates from searchCriteria, reformat them, and put them into the map
        SimpleDateFormat fromFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat toFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            Date checkInDate = fromFormat.parse(searchCriteria.getCheckIn());
            String reformattedCheckIn = toFormat.format(checkInDate);
            paramsMap.put("checkin_date", reformattedCheckIn);

            Date checkOutDate = fromFormat.parse(searchCriteria.getCheckOut());
            String reformattedCheckOut = toFormat.format(checkOutDate);
            paramsMap.put("checkout_date", reformattedCheckOut);
        } catch (ParseException e) {
            logger.warn("error occurred in Creating Params map: " + e.getMessage(), e);
        }
        if(PAGE_CONTEXT_LANDING.equalsIgnoreCase(pageContext)){
            // Concatenate roomCount, adultCount, and childCount with "-" in between and put the resulting string into the map
            String qd = roomCount + "-" + adultCount + "-" + childCount;
            paramsMap.put("qd", qd);
        }
        return paramsMap;
    }

    /**
     * Fetches smart filter tags based on the provided search query.
     *
     * @param filterCountRequest The request to get smart filter tags for
     * @param allFilters 
     * @return List of smart filter tag strings
     * @throws ClientGatewayException if there's an error fetching the smart filter tags
     */
    public List<String> getSmartFilterTagsForQuery(FilterCountRequest filterCountRequest, List<com.mmt.hotels.clientgateway.response.filter.Filter> allFilters) throws ClientGatewayException {
        String searchQueryText = filterCountRequest.getSearchCriteria().getSearchQueryText();

        try {
            Map<String, String> headers = new HashMap<>();
            // Use request transformer pattern in service layer
            SmartFiltersRequestTransformer requestTransformer = smartFiltersFactory.getRequestService(filterCountRequest.getClient());
            SmartFiltersRequest smartFiltersRequest = requestTransformer.convertSmartFiltersRequest(filterCountRequest, allFilters);
            
            // Call executor with transformed request
            String response = filterExecutor.getSmartFilterTags(smartFiltersRequest, headers, String.class);
            
            if (StringUtils.isNotEmpty(response)) {
                // Parse the response to extract filter tags
                // Assuming the response is a JSON array of strings or a comma-separated string
                List<String> filterTags = objectMapperUtil.getObjectFromJsonWithType(
                    response, 
                    new TypeReference<List<String>>() {}, 
                    DependencyLayer.CHATBOT
                );
                return filterTags != null ? filterTags : new ArrayList<>();
            }
            
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("Error occurred while fetching smart filter tags for query: " + searchQueryText, e);
            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }

    /**
     * Gets smart filters based on the provided filter count request.
     *
     * @param filterCountRequest The request to get smart filters for
     * @param parameterMap Map of request parameters
     * @param httpHeaderMap Map of HTTP headers
     * @param seoCorp SEO corporate flag
     * @return SmartFiltersResponse containing matching filters
     * @throws ClientGatewayException if there's an error processing the request
     */
    public SmartFiltersResponse getSmartFilters(FilterCountRequest filterCountRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, Boolean seoCorp) throws ClientGatewayException {
        try {
            // Get filter response
            FilterResponse response = filterCount(filterCountRequest, parameterMap, httpHeaderMap, seoCorp);
            
            // Extract all filters and get matching ones in one pass
            List<com.mmt.hotels.clientgateway.response.filter.Filter> allFilters = utility.getAllFiltersFromList(response.getFilterList(),response.getFilterPills());
            
            if (CollectionUtils.isEmpty(allFilters)) {
                return smartFiltersFactory.getResponseService(filterCountRequest.getClient())
                        .convertSmartFiltersResponse(new ArrayList<>(), new ArrayList<>(), filterCountRequest);
            }
            
            // Get filter titles and smart filter tags
            List<String> filterTags = getSmartFilterTagsForQuery(filterCountRequest, allFilters);
            
            // Use response transformer pattern - handles transformation from filter tags to matching filters
            return smartFiltersFactory.getResponseService(filterCountRequest.getClient())
                    .convertSmartFiltersResponse(filterTags, allFilters, filterCountRequest);
            
        } catch (Throwable e) {
            if (e instanceof ErrorResponseFromDownstreamException) {
                logger.error("error occurred in getSmartFilters: " + e.getMessage());
                logger.debug("error occurred in getSmartFilters: " + e.getMessage(), e);
            } else
                logger.error("error occurred in getSmartFilters: " + e.getMessage(), e);

            ExceptionHandlerResponse exceptionHandlerResponse = ExceptionHandler.handleException(e);
            metricErrorLogger.logErrorInMetric(exceptionHandlerResponse.getMetricError(), MDC.getCopyOfContextMap());
            throw exceptionHandlerResponse.getClientGatewayException();
        }
    }
    

    

}
