package com.mmt.hotels.clientgateway.service;

import com.gommt.hotels.orchestrator.SortOrder;
import com.gommt.hotels.orchestrator.enums.Currency;
import com.gommt.hotels.orchestrator.enums.TrafficSource;
import com.gommt.hotels.orchestrator.enums.*;
import com.gommt.hotels.orchestrator.model.objects.BookingDevice;
import com.gommt.hotels.orchestrator.model.objects.RoomDetails;
import com.gommt.hotels.orchestrator.model.request.common.SortCriteria;
import com.gommt.hotels.orchestrator.model.request.common.*;
import com.gommt.hotels.orchestrator.model.request.listing.FilterDetails;
import com.gommt.hotels.orchestrator.model.request.listing.FilterLocationDetails;
import com.gommt.hotels.orchestrator.model.request.listing.FilterLocationType;
import com.gommt.hotels.orchestrator.model.request.listing.ListingRequest;
import com.gommt.hotels.orchestrator.model.response.listing.ListingResponse;
import com.gommt.hotels.orchestrator.model.state.ImageDetails;
import com.gommt.hotels.orchestrator.model.state.UserDetails;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.exception.ClientGatewayException;
import com.mmt.hotels.clientgateway.exception.ErrorResponseFromDownstreamException;
import com.mmt.hotels.clientgateway.helpers.CommonHelper;
import com.mmt.hotels.clientgateway.request.*;
import com.mmt.hotels.clientgateway.request.dayuse.Slot;
import com.mmt.hotels.clientgateway.response.searchHotels.SearchHotelsResponse;
import com.mmt.hotels.clientgateway.restexecutors.OrchSearchHotelsExecutor;
import com.mmt.hotels.clientgateway.thirdparty.response.ExtendedUser;
import com.mmt.hotels.clientgateway.thirdparty.response.HydraResponse;
import com.mmt.hotels.clientgateway.transformer.factory.SearchHotelsFactory;
import com.mmt.hotels.clientgateway.util.*;
import com.mmt.hotels.filter.FilterGroup;
import com.mmt.hotels.model.request.UserLocation;
import com.mmt.hotels.model.request.matchmaker.InputHotel;
import com.mmt.hotels.model.request.matchmaker.MatchMakerRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import com.mmt.hotels.clientgateway.enums.LocationType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.mmt.hotels.clientgateway.constants.Constants.BEDROOM_COUNT;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_RECOMMENDATION;
import static com.mmt.hotels.clientgateway.constants.Constants.EXACT_ROOM_VALUE;
import static com.mmt.hotels.clientgateway.constants.ControllerConstants.LISTING_SEARCH_HOTELS;

@Service
public class OrchListingService {


    @Autowired
    private CommonHelper commonHelper;

    private static final Logger LOGGER = LoggerFactory.getLogger(OrchListingService.class);

    @Autowired
    Utility utility;
    @Autowired
    private OrchSearchHotelsExecutor orchSearchHotelsExecutor;
    @Autowired
    private MetricAspect metricAspect;
    @Autowired
    private MetricErrorLogger metricErrorLogger;

    @Autowired
    private SearchHotelsFactory searchHotelsFactory;

    public OrchListingService(CommonHelper commonHelper) {
        this.commonHelper = commonHelper;
    }

    public SearchHotelsResponse searchHotels(SearchHotelsRequest searchHotelsRequest, CommonModifierResponse commonModifierResponse, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();
            if (searchHotelsRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(searchHotelsRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getSearchCriteria().getCheckIn(),
                        searchHotelsRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);


            ListingRequest searchWrapperInputRequest = buildSearchHotelsRequest(searchHotelsRequest, commonModifierResponse);

            ListingResponse listingResponse = orchSearchHotelsExecutor.searchHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_RESPONSE_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);

            return searchHotelsFactory.getSearchHotelsResponseService(searchHotelsRequest.getClient())
                    .convertSearchHotelsResponse(listingResponse, searchHotelsRequest, commonModifierResponse);

        } catch (Throwable e) {
            LOGGER.error("error occurred in searchHotels: {}", e.getMessage());
            LOGGER.debug("error occurred in searchHotels: {}", e.getMessage(), e);
            throw e;
        }

    }


    public String searchHotelsScion(SearchHotelsRequest searchHotelsRequest, Map<String, String[]> parameterMap, Map<String, String> httpHeaderMap, CommonModifierResponse commonModifierResponse) throws ClientGatewayException {
        try {
            long startTime = System.currentTimeMillis();

            if (searchHotelsRequest.getSearchCriteria() != null) {
                utility.setPaginatedToMDC(searchHotelsRequest.getSearchCriteria());
                utility.setLoggingParametersToMDC(searchHotelsRequest.getSearchCriteria().getRoomStayCandidates(), searchHotelsRequest.getSearchCriteria().getCheckIn(),
                        searchHotelsRequest.getSearchCriteria().getCheckOut());
            }
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_COMMON_REQUEST_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);

            ListingRequest searchWrapperInputRequest = buildSearchHotelsRequest(searchHotelsRequest, commonModifierResponse);

            ListingResponse listingResponse = orchSearchHotelsExecutor.searchHotels(searchWrapperInputRequest, parameterMap, httpHeaderMap);
            //RESPONSE TIME
            metricAspect.addToTimeInternalProcess(Constants.PROCESS_SEARCH_RESPONSE_PROCESS, LISTING_SEARCH_HOTELS, System.currentTimeMillis() - startTime);
            return searchHotelsFactory.getSearchHotelsScionTransformer(searchHotelsRequest.getClient()).convertSearchHotelsResponse(searchHotelsRequest, listingResponse);

        } catch (Throwable e) {
            LOGGER.error("error occurred in searchHotelsScion: {}", e.getMessage());
            LOGGER.debug("error occurred in searchHotelsScion: {}", e.getMessage(), e);
            throw e;
        }
    }

    public ListingRequest buildSearchHotelsRequest(ListingSearchRequest cgRequest, CommonModifierResponse commonModifierResponse) {
        if (cgRequest == null) {
            LOGGER.error("ListingSearchRequest is null");
            throw new IllegalArgumentException("ListingSearchRequest cannot be null");
        }

        if (commonModifierResponse == null) {
            LOGGER.error("CommonModifierResponse is null");
            throw new IllegalArgumentException("CommonModifierResponse cannot be null");
        }

        ListingRequest orchRequest = new ListingRequest();

        // Build location details
        orchRequest.setLocation(buildLocationDetails(cgRequest));

        // Extract search criteria safely
        SearchHotelsCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElseThrow(() -> new IllegalArgumentException("SearchHotelsCriteria cannot be null"));

        // Set check-in, check-out, and other parameters
        orchRequest.setCheckIn(searchCriteria.getCheckIn());
        orchRequest.setCheckOut(searchCriteria.getCheckOut());
        orchRequest.setLastHotelId(searchCriteria.getLastHotelId());
        orchRequest.setLastFetchedWindowInfo(searchCriteria.getLastFetchedWindowInfo());
        orchRequest.setLimit(searchCriteria.getLimit());
        orchRequest.setSlotDetails(buildSlotDetails(searchCriteria.getSlot()));
        orchRequest.setUserSearchType(searchCriteria.getUserSearchType());
        orchRequest.setSelectedTabId(cgRequest.getSelectedTabId());

        //Direct Hotel Search
        orchRequest.setDirectHotelId(getDirectHotelId(cgRequest.getMatchMakerDetails()));

        // Set SEO-related fields TODO: Where is this picked?
//        orchRequest.setSeoCohort(Optional.ofNullable(cgRequest.getFeatureFlags())
//                .map(FeatureFlags::getSeoCohort)
//                .orElse(""));
//
//        // TODO: Where is this picked?
//        orchRequest.setSeoDescTemplate("");

        // TODO: AG ? (Setting B2B attributes as empty map)
        orchRequest.setB2bAttributes(Collections.emptyMap());

        // Build room criteria (Room Stay Candidates)
        orchRequest.setRooms(buildRoomDetails(cgRequest));

        // Build client details
        orchRequest.setClientDetails(buildClientDetails(cgRequest, commonModifierResponse));

        //Update Filter Flags:
        updateFilterFlags(cgRequest.getFilterCriteria());
        // Build filters
        orchRequest.setFilters(buildFilterDetails(cgRequest));

        // Build traveller details
//        orchRequest.setTravellerDetails(buildTravellerDetailRequest(cgRequest, commonModifierResponse));

        // Build sort criteria
        orchRequest.setSortCriteria(buildSortCriteria(cgRequest));

        // Build image details todo decide if this is needed
        orchRequest.setImageDetails(buildImageDetails(cgRequest));

        // Set experiment data and additional fields
        orchRequest.setExperimentData(cgRequest.getExpData());
        orchRequest.setExpVariantKeys(cgRequest.getExpVariantKeys());
        orchRequest.setManthanExpDataMap(commonModifierResponse.getManthanExpDataMap());
        orchRequest.setValidExpList(cgRequest.getValidExpList());

        //TODO NOT VALID FOR GI AS OF NOW
//        orchRequest.setContentExpDataMap(commonModifierResponse.getContentExpDataMap());

        LOGGER.info("Successfully built ListingRequest");
        return orchRequest;
    }

    private SlotDetails buildSlotDetails(Slot slot) {
        SlotDetails slotDetails = new SlotDetails();
        if(slot==null){
            return slotDetails;
        }
        slotDetails.setTimeSlot(slot.getTimeSlot()!=null?slot.getTimeSlot():0);
        slotDetails.setDuration(slot.getDuration()!=null?slot.getDuration():0);
        return slotDetails;
    }

    private String getDirectHotelId(MatchMakerRequest matchMakerDetails) {
        if (matchMakerDetails == null) {
            return null;
        }
        List<InputHotel> inputHotels = matchMakerDetails.getHotels();
        if (CollectionUtils.isEmpty(inputHotels)) {
            return null;
        }
        return inputHotels.get(0).getHotelId();
    }

    private void updateFilterFlags(List<Filter> filterCriteria){
        if(CollectionUtils.isNotEmpty(filterCriteria)){
            Filter filter = filterCriteria.get(filterCriteria.size() - 1);
            filter.setLastApplied(true);
        }
    }

    private LocationDetails buildLocationDetails(ListingSearchRequest cgRequest) {
        if (cgRequest == null) {
            LOGGER.warn("ListingSearchRequest is null while building LocationDetails, returning empty LocationDetails.");
            return new LocationDetails();  // Return empty LocationDetails if the request is null
        }

        SearchHotelsCriteria searchCriteria = Optional.ofNullable(cgRequest.getSearchCriteria())
                .orElse(new SearchHotelsCriteria());  // Return a default SearchHotelsCriteria if null

        LocationDetails locationDetails = new LocationDetails();

        // Set location details from search criteria, defaulting to empty strings if values are not present
        locationDetails.setId(Optional.ofNullable(searchCriteria.getLocationId()).orElse(""));  // Default to empty string if null
        locationDetails.setType(Optional.ofNullable(searchCriteria.getLocationType()).orElse(""));  // Default to empty string if null
        locationDetails.setCityId(Optional.ofNullable(searchCriteria.getCityCode()).orElse(""));  // CityCode defaults to empty if not provided
        locationDetails.setCityName(Optional.ofNullable(searchCriteria.getCityName()).orElse(""));  // CityName defaults to empty if not provided
        locationDetails.setCountryId(Optional.ofNullable(searchCriteria.getCountryCode()).orElse(""));  // CountryCode defaults to empty if null

        // Initialize GeoLocationDetails with a default constructor as per the assumption

        GeoLocationDetails geoLocationDetails = new GeoLocationDetails();
        geoLocationDetails.setLatitude(searchCriteria.getLat() != null ? String.valueOf(searchCriteria.getLat()) : "");
        geoLocationDetails.setLongitude(searchCriteria.getLng() != null ? String.valueOf(searchCriteria.getLng()) : "");
        locationDetails.setGeo(geoLocationDetails);

        // TODO: Set stateId if required once available
//         locationDetails.setStateId(searchCriteria.getStateId());

        LOGGER.info("Successfully built LocationDetails.");
        return locationDetails;
    }


    private List<RoomDetails> buildRoomDetails(ListingSearchRequest searchHotelsRequestGateway) {
        if (searchHotelsRequestGateway == null || searchHotelsRequestGateway.getSearchCriteria() == null) {
            LOGGER.warn("ListingSearchRequest or SearchHotelsCriteria is null while building RoomDetails, returning an empty RoomDetails list.");
            return Collections.emptyList();  // Return an empty list if the request or search criteria is null
        }

        List<RoomStayCandidate> roomStayCandidates = searchHotelsRequestGateway.getSearchCriteria().getRoomStayCandidates();

        if (roomStayCandidates == null || roomStayCandidates.isEmpty()) {
            LOGGER.warn("RoomStayCandidates is null or empty, returning an empty RoomDetails list.");
            return Collections.emptyList();  // Return empty list if no candidates are provided
        }

        //TODO - Not required for GI as of now
      /*  if (searchHotelsRequestGateway.getSearchCriteria() != null && utility.isDistributeRoomStayCandidates(searchHotelsRequestGateway.getSearchCriteria().getRoomStayCandidates())) {
            List<com.mmt.hotels.model.request.RoomStayCandidate> roomStayCandidatesHES = utility.buildRoomStayDistribution(searchHotelsRequestGateway.getSearchCriteria().getRoomStayCandidates());
            return roomStayCandidatesHES.stream().map(roomStayCandidate -> {
                RoomDetails roomDetails = new RoomDetails();
                int adultCount = 0;
                List<Integer> childrenAges = new ArrayList<>();
                for(GuestCount guestCount : roomStayCandidate.getGuestCounts()) {
                    adultCount = adultCount + Integer.parseInt(guestCount.getCount());
                    if(CollectionUtils.isNotEmpty(guestCount.getAges())) {
                        childrenAges.addAll(guestCount.getAges());
                    }
                }
                roomDetails.setAdults(adultCount);
                roomDetails.setChildrenAges(childrenAges);
                return roomDetails;
            }).collect(Collectors.toList());
        }*/



        List<RoomDetails> roomDetailsList = new ArrayList<>();

        // Iterate over room stay candidates and build room details
        for (RoomStayCandidate roomStayCandidate : roomStayCandidates) {
            if (roomStayCandidate != null) {
                RoomDetails roomDetails = new RoomDetails();
                roomDetails.setAdults(roomStayCandidate.getAdultCount());
                roomDetails.setChildrenAges(CollectionUtils.isEmpty(roomStayCandidate.getChildAges()) ?
                        new ArrayList<>(): roomStayCandidate.getChildAges());
                roomDetailsList.add(roomDetails);
            } else {
                LOGGER.warn("Encountered a null RoomStayCandidate, skipping.");
            }
        }

        LOGGER.info("Successfully built RoomDetails for {} rooms.", roomDetailsList.size());
        return roomDetailsList;
    }


    private List<FilterDetails> buildFilterDetails(ListingSearchRequest searchHotelsRequestGateway) {
        if (searchHotelsRequestGateway == null) {
            LOGGER.warn("ListingSearchRequest is null while building FilterDetails, returning an empty FilterDetails list.");
            return Collections.emptyList();  // Return an empty list if the request is null
        }

        //Create a Filter Map ->
        Map<String, FilterDetails> filterMap = new HashMap<>();

        // Extract filter criteria, default to empty list if null
        List<Filter> filterCriteriaList = Optional.ofNullable(searchHotelsRequestGateway.getFilterCriteria())
                .orElse(Collections.emptyList());

        // Initialize the list for filter details
        List<FilterDetails> filterDetailsList = new ArrayList<>();

        // Iterate through each filter criterion and build filter details
        filterCriteriaList.stream().filter(Objects::nonNull).forEach(filterCriteria -> {
            String key = filterCriteria.getFilterGroup().name();
            if (EXACT_ROOM_RECOMMENDATION.equalsIgnoreCase(key) || (BEDROOM_COUNT.equalsIgnoreCase(key))) {
                return;
            }

            FilterDetails filterDetails;
            if (filterMap.containsKey(key)) {
                filterDetails = filterMap.get(key);
                updateFilterDetails(filterCriteria, filterDetails);
            } else {
                filterDetails = initializeFilterDetails(filterCriteria);
                filterMap.put(key, filterDetails);
            }
        });


        FilterDetails matchMakerRequestFilter = createMatchMakerRequestFilter(searchHotelsRequestGateway);
        if (matchMakerRequestFilter != null) {
            filterMap.put(matchMakerRequestFilter.getGroup(), matchMakerRequestFilter);
        }
        //Convert the entry set of the map to a list
        filterDetailsList = new ArrayList<>(filterMap.values());


        LOGGER.info("Successfully built FilterDetails for {} filters.", filterDetailsList.size());
        return filterDetailsList;
    }

    private FilterDetails createMatchMakerRequestFilter(ListingSearchRequest searchHotelsRequestGateway){
        // Step 1: Create custom filter for Matchmaker
        FilterDetails filterForMatchmakerObject = createFilterForMatchmakerObject(searchHotelsRequestGateway);

        // Step 3: Modify MatchMaker details if distance filter is applied
        filterForMatchmakerObject = modifyMatchMakerDetailsIfDistanceFilterApplied(searchHotelsRequestGateway, filterForMatchmakerObject);

        return filterForMatchmakerObject;

    }
    private FilterDetails modifyMatchMakerDetailsIfDistanceFilterApplied(ListingSearchRequest searchHotelsRequestGateway, FilterDetails matchMakerObject) {

        if (searchHotelsRequestGateway == null || CollectionUtils.isEmpty(searchHotelsRequestGateway.getFilterCriteria())) {
            return matchMakerObject;
        }

        Optional<Filter> distanceFilterOptional = searchHotelsRequestGateway.getFilterCriteria().stream()
                .filter(filter -> filter != null && FilterGroup.DRIVE_WALK_PROXIMITY_KEY_POI.name().equalsIgnoreCase(filter.getFilterGroup().name()))
                .findFirst();
        FilterLocationDetails resultObject = null;
        if (distanceFilterOptional.isPresent()) {
            FilterLocationDetails.FilterLocationDetailsBuilder filterLocationDetailsBuilder = FilterLocationDetails.builder();
            filterLocationDetailsBuilder.type(FilterLocationType.POI);
            Filter distanceFilter = distanceFilterOptional.get();
            if (StringUtils.isNotEmpty(distanceFilter.getFilterValue()) && distanceFilter.getFilterValue().contains(Constants.HASH_SEPARATOR)) {
                String[] splitValue = distanceFilter.getFilterValue().split(Constants.HASH_SEPARATOR);
                if (splitValue.length == 2 && splitValue[0].contains(Constants.UNDERSCORE)) {
                    String[] poiIds = splitValue[0].split(Constants.UNDERSCORE);
                    if (poiIds.length == 2 && StringUtils.isNotEmpty(poiIds[1])) {
                        filterLocationDetailsBuilder.id(poiIds[1]);
                        resultObject = filterLocationDetailsBuilder.build();
                    }
                }
            }
        }
        if (resultObject != null) {
            if(matchMakerObject==null)
                matchMakerObject = initializeMatchMakerFilter();
            List<FilterLocationDetails> filterLocationDetailsList = Optional.ofNullable(matchMakerObject).map(FilterDetails::getLocation).orElse(new ArrayList<>());
            filterLocationDetailsList.add(resultObject);
            assert matchMakerObject != null;
            matchMakerObject.setLocation(filterLocationDetailsList);
        }
        return matchMakerObject;
    }


    public FilterDetails createFilterForMatchmakerObject(ListingSearchRequest cgRequest) {

        boolean isPoiMultiCitySearch = false;
        boolean isAreaMultiCitySearch = false;
        if (cgRequest != null && cgRequest.getSearchCriteria() != null &&
                StringUtils.isNotBlank(cgRequest.getSearchCriteria().getLocationType())) {
            String locationType = cgRequest.getSearchCriteria().getLocationType();
            isPoiMultiCitySearch = LocationType.poi.name().equalsIgnoreCase(locationType);
            isAreaMultiCitySearch = LocationType.area.name().equalsIgnoreCase(locationType);
        }

        String locationId = (cgRequest != null && cgRequest.getSearchCriteria() != null) ? cgRequest.getSearchCriteria().getLocationId() : null;

        // Set MatchMakerRequest safely
            if (cgRequest != null && isMatchMakerDetailsEmpty(cgRequest.getMatchMakerDetails())) {

                if((isPoiMultiCitySearch || isAreaMultiCitySearch)) {
                   // If matchMakerDetails exists but all lists are empty AND multi-city search is enabled, use multi-city logic
                   return createMatchMakerFilterForMultiCity(isPoiMultiCitySearch, isAreaMultiCitySearch, locationId);
               }
                return null;
            }



        FilterDetails filterDetails = initializeMatchMakerFilter();
        if(cgRequest != null) {
            List<FilterLocationDetails> filterLocationDetailsList = convertMatchMakerToFilterLocation(cgRequest.getMatchMakerDetails());
            filterDetails.setLocation(filterLocationDetailsList);
        }
        return filterDetails;
    }

    private FilterDetails createMatchMakerFilterForMultiCity(boolean isPoiMultiCitySearch, boolean isAreaMultiCitySearch, String locationId) {
        // Only proceed if locationId is not null or empty
        if (StringUtils.isEmpty(locationId)) {
            LOGGER.debug("LocationId is null or empty, returning null for multi-city location filter");
            return null;
        }

        FilterDetails filterDetails = initializeMatchMakerFilter();
        List<FilterLocationDetails> filterLocationDetailsList = new ArrayList<>();

        // Create FilterLocationDetails based on search type
        FilterLocationDetails.FilterLocationDetailsBuilder builder = FilterLocationDetails.builder();
        builder.id(locationId);

        if (isPoiMultiCitySearch) {
            builder.type(FilterLocationType.POI);
        } else if (isAreaMultiCitySearch) {
            builder.type(FilterLocationType.AREA);
        }

        filterLocationDetailsList.add(builder.build());
        filterDetails.setLocation(filterLocationDetailsList);

        return filterDetails;
    }

    public FilterDetails initializeMatchMakerFilter(){
        FilterDetails filterDetails = new FilterDetails();
        filterDetails.setGroup("MATCHMAKER");
        return filterDetails;
    }

    private List<FilterLocationDetails> convertMatchMakerToFilterLocation(MatchMakerRequest matchMakerDetails) {
        if (matchMakerDetails == null) {
            return null;
        }
        List<FilterLocationDetails> filterLocationDetailsList = new ArrayList<>();
        //POI
        List<com.mmt.hotels.model.request.matchmaker.LatLngObject> latLng = matchMakerDetails.getLatLng();
        if (latLng != null && !latLng.isEmpty()) {
            filterLocationDetailsList.addAll(latLng.stream().map(this::createPoiObject).collect(Collectors.toList()));
        }
        //AREA
        List<com.mmt.hotels.model.request.matchmaker.Tags> selectedTags = matchMakerDetails.getSelectedTags();
        if (selectedTags != null && !selectedTags.isEmpty()) {
            for (com.mmt.hotels.model.request.matchmaker.Tags tag : selectedTags) {
                filterLocationDetailsList.add(createAreaObject(tag));
            }
        }
        return filterLocationDetailsList;
    }


    private boolean isMatchMakerDetailsEmpty(MatchMakerRequest matchMakerDetails) {
        if (matchMakerDetails == null) {
            return true;
        }

        boolean isLatLngEmpty = CollectionUtils.isEmpty(matchMakerDetails.getLatLng());
        boolean isSelectedTagsEmpty = CollectionUtils.isEmpty(matchMakerDetails.getSelectedTags());
        boolean isHotelsEmpty = CollectionUtils.isEmpty(matchMakerDetails.getHotels());
        boolean isCityTagsEmpty = CollectionUtils.isEmpty(matchMakerDetails.getCityTags());

        return isLatLngEmpty && isSelectedTagsEmpty && isHotelsEmpty && isCityTagsEmpty;
    }

    public FilterLocationDetails createPoiObject(com.mmt.hotels.model.request.matchmaker.LatLngObject latLng) {
        FilterLocationDetails.FilterLocationDetailsBuilder filterLocationDetails = FilterLocationDetails.builder();
        filterLocationDetails.id(latLng.getPoiId())
                .type(FilterLocationType.POI)
                .name(latLng.getName())
                .geo(buildGeo(String.valueOf(latLng.getLatitude()), String.valueOf(latLng.getLongitude())))
                .build();
        return filterLocationDetails.build();
    }

    public GeoLocationDetails buildGeo(String lat, String lng) {
        GeoLocationDetails geoLocationDetails = new GeoLocationDetails();
        geoLocationDetails.setLongitude(lng);
        geoLocationDetails.setLatitude(lat);
        return geoLocationDetails;
    }

    public FilterLocationDetails createAreaObject(com.mmt.hotels.model.request.matchmaker.Tags tag) {
        FilterLocationDetails.FilterLocationDetailsBuilder filterLocationDetails = FilterLocationDetails.builder();
        return filterLocationDetails.id(tag.getTagAreaId())
                .type(FilterLocationType.AREA)
                .name(tag.getTagDescription())
                .build();
    }

    // Method to initialize a new FilterDetails object
    private FilterDetails initializeFilterDetails(Filter filterCriteria) {
        FilterDetails filterDetails = new FilterDetails();
        filterDetails.setGroup(filterCriteria.getFilterGroup().name());

        Set<String> filterValues = new HashSet<>();
        Optional.ofNullable(filterCriteria.getFilterValue()).ifPresent(filterValues::add);
        filterDetails.setValues(filterValues);

        if (filterCriteria.getFilterRange() != null) {
            RangeDetails rangeDetails = RangeDetails.builder()
                    .min(Optional.ofNullable(filterCriteria.getFilterRange().getMinValue()).orElse(0))
                    .max(Optional.ofNullable(filterCriteria.getFilterRange().getMaxValue()).orElse(0))
                    .build();
            filterDetails.setRange(Collections.singletonList(rangeDetails));
        } else {
            filterDetails.setRange(Collections.emptyList());
        }
        return filterDetails;
    }

    // Method to update an existing FilterDetails object
    private void updateFilterDetails(Filter filterCriteria, FilterDetails filterDetails) {
        Set<String> filterValues = filterDetails.getValues();
        Optional.ofNullable(filterCriteria.getFilterValue()).ifPresent(filterValues::add);
        filterDetails.setValues(filterValues);

        if (filterCriteria.getFilterRange() != null) {
            RangeDetails rangeDetails = RangeDetails.builder()
                    .min(Optional.ofNullable(filterCriteria.getFilterRange().getMinValue()).orElse(0))
                    .max(Optional.ofNullable(filterCriteria.getFilterRange().getMaxValue()).orElse(0))
                    .build();
            filterDetails.setRange(Collections.singletonList(rangeDetails));
        }
    }


    /*private TravellerDetailRequest buildTravellerDetailRequest(ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
        //TODO For MyBiz Requests only
        TravellerDetailRequest travellerDetailRequest = new TravellerDetailRequest();
        return travellerDetailRequest;
    }*/

    private SortCriteria buildSortCriteria(ListingSearchRequest searchHotelsRequestGateway) {
        SortCriteria sortCriteria = null;
        if (searchHotelsRequestGateway.getSortCriteria() != null) {
            sortCriteria = new SortCriteria();
            sortCriteria.setOrder(SortOrder.valueOf(searchHotelsRequestGateway.getSortCriteria().getOrder()));
            sortCriteria.setField(searchHotelsRequestGateway.getSortCriteria().getField());
        }
        return sortCriteria;
    }

    private ImageDetails buildImageDetails(ListingSearchRequest searchHotelsRequestGateway) {
        ImageDetails imageDetails = new ImageDetails();
        // Safely retrieve and set categories, defaulting to an empty list if null
        List<ImageCategory> categories = Optional.ofNullable(searchHotelsRequestGateway.getImageDetails())
                .map(com.mmt.hotels.clientgateway.request.ImageDetails::getCategories)
                .orElse(Collections.emptyList());
        imageDetails.setCategories(buildImageCategory(categories));

        // Safely set types, defaulting to an empty list if null
        List<String> types = Optional.ofNullable(searchHotelsRequestGateway.getImageDetails())
                .map(com.mmt.hotels.clientgateway.request.ImageDetails::getTypes)
                .orElse(null);
        imageDetails.setTypes(types);

        return imageDetails;
    }


    private List<com.gommt.hotels.orchestrator.model.objects.ImageCategory> buildImageCategory(List<ImageCategory> inputCategories) {
        List<com.gommt.hotels.orchestrator.model.objects.ImageCategory> imageCategoryList = null;
        for (ImageCategory imageCategory : inputCategories) {
            imageCategoryList = new ArrayList<>();
            com.gommt.hotels.orchestrator.model.objects.ImageCategory result = new com.gommt.hotels.orchestrator.model.objects.ImageCategory();
            result.setType(imageCategory.getType());
            result.setCount(imageCategory.getCount());
            result.setHeight(imageCategory.getHeight());
            result.setImageFormat(imageCategory.getImageFormat());
            result.setWidth(imageCategory.getWidth());
            imageCategoryList.add(result);
        }
        return imageCategoryList;
    }

    private ClientDetails buildClientDetails(ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
        ClientDetails clientDetails = new ClientDetails();
        clientDetails.setFeatureFlags(buildFeatureFlags(searchHotelsRequestGateway, commonModifierResponse));
        clientDetails.setRequestDetails(buildRequestDetails(searchHotelsRequestGateway, commonModifierResponse));
        clientDetails.setUserDetails(buildUserDetails(searchHotelsRequestGateway, commonModifierResponse));
        clientDetails.setVisitorId(searchHotelsRequestGateway.getRequestDetails().getVisitorId());
        clientDetails.setMcId(commonModifierResponse.getMcId());
        return clientDetails;
    }


    private com.gommt.hotels.orchestrator.model.state.FeatureFlags buildFeatureFlags(ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
        if (searchHotelsRequestGateway == null) {
            LOGGER.warn("ListingSearchRequest is null while building FeatureFlags, returning an empty FeatureFlags.");
            return new com.gommt.hotels.orchestrator.model.state.FeatureFlags();  // Return empty FeatureFlags if request is null
        }

        FeatureFlags featureFlags = Optional.ofNullable(searchHotelsRequestGateway.getFeatureFlags()).orElse(new FeatureFlags());
        com.gommt.hotels.orchestrator.model.state.FeatureFlags result = new com.gommt.hotels.orchestrator.model.state.FeatureFlags();

        // Safely set FlightBooker, defaulting to false
        result.setFlightBooker(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::isFlightBooker)
                .orElse(false));

        // Safely set TrendingNow, defaulting to false
        result.setTrendingNow(Optional.ofNullable(searchHotelsRequestGateway.getSearchCriteria())
                .map(SearchHotelsCriteria::getCollectionCriteria)
                .map(CollectionCriteria::isTrendingNow)
                .orElse(false));

        // Safely set WalletRequired, defaulting to false
        result.setWalletRequired(Optional.of(featureFlags).map(FeatureFlags::isWalletRequired).orElse(false));

        // Safely set CityTaxExclusive based on condition
        result.setCityTaxExclusive(Optional.of(commonModifierResponse).map(CommonModifierResponse::isCityTaxExclusive).orElse(false));

        // Override CityTaxExclusive if CORP_ID_CONTEXT matches
        result.setCityTaxExclusive(!Constants.CORP_ID_CONTEXT.equalsIgnoreCase(
                Optional.ofNullable(searchHotelsRequestGateway.getRequestDetails())
                        .map(RequestDetails::getIdContext)
                        .orElse("")) && result.isCityTaxExclusive());

        // Safely set BestCoupon, defaulting to false
        result.setBestCoupon(Optional.of(featureFlags).map(FeatureFlags::isCoupon).orElse(false));

        // Safely set ComparatorHotelRequest, defaulting to false
        result.setComparatorHotelRequest(Optional.of(featureFlags).map(FeatureFlags::isComparator).orElse(false));

        // Check RoomPreferenceEnabled from applied filters, defaulting to false
        result.setRoomPreferenceEnabled(utility.checkIfFilterValueExistsInAppliedFilterMap(
                searchHotelsRequestGateway.getFilterCriteria()));

        if (utility.isExperimentTrue(searchHotelsRequestGateway.getExpDataMap(), Constants.EXP_KEY_EXACT_ROOM_SEARCH)) {
            result.setRoomPreferenceEnabled(!result.isRoomPreferenceEnabled());
        }

        // Safely set CollectionRequest, defaulting to false
        result.setCollectionRequest(Optional.ofNullable(searchHotelsRequestGateway.getSearchCriteria())
                .map(SearchHotelsCriteria::getCollectionCriteria)
                .map(CollectionCriteria::isCollectionRequired)
                .orElse(false));

        // Hardcoded value for AdvancedFiltering
        result.setAdvancedFiltering(Boolean.TRUE);

        // Safely set CheckAvailability, defaulting to false
        result.setCheckAvailability(Optional.of(featureFlags).map(FeatureFlags::isCheckAvailability).orElse(false));

        // Safely set HomeStayV2Flow, defaulting to false todo : Not needed for GI
//        result.setHomeStayV2Flow(Optional.of(commonModifierResponse).map(CommonModifierResponse::isHomestayV2Flow).orElse(false));

        // Safely set Orientation, defaulting to null if not present
        result.setOrientation(Optional.of(featureFlags).map(FeatureFlags::getOrientation).orElse(null));

        // Safely set SimilarHotels, defaulting to false
        result.setSimilarHotels(Optional.of(featureFlags).map(FeatureFlags::isSimilarHotel).orElse(false));

        // TODO: These feature flags were not found on CG, defaulting to false
        result.setAllSoldOutRequired(false);
        result.setSoldOutInfoRequired(false);
        result.setListingMapShortStays(false);
        result.setBookingModification(false);
        result.setAddHCPToHotelDiscount(false);
        result.setEnableSabre(false);
        result.setBnplExtended(false);
        result.setBlockEmi(false);
        result.setSoldOut(false);
        result.setScarcityFlow(false);
        result.setUnmodifiedAmenities(false);
        result.setSameCityFlightItineraryAvailable(false);
        result.setLocus(false);
        result.setBlackUser(false);
        result.setMmtPrime(false);
        result.setDoubleBlackUser(false);
        result.setLimitedFilterCall(false);
        result.setPremiumThemesCardRequired(featureFlags.isPremiumThemesCardRequired());

        LOGGER.info("Successfully built FeatureFlags.");
        return result;
    }

    private com.gommt.hotels.orchestrator.model.state.RequestDetails buildRequestDetails(ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
        if (searchHotelsRequestGateway == null) {
            LOGGER.warn("ListingSearchRequest is null while building RequestDetails, returning empty RequestDetails.");
            return new com.gommt.hotels.orchestrator.model.state.RequestDetails();  // Return empty RequestDetails if the request is null
        }

        com.gommt.hotels.orchestrator.model.state.RequestDetails requestDetails = new com.gommt.hotels.orchestrator.model.state.RequestDetails();

        // Safely retrieve BookingDevice from deviceDetails, defaulting to an empty device
        requestDetails.setBookingDevice(buildBookingDevice(Optional.ofNullable(searchHotelsRequestGateway.getDeviceDetails()).orElse(new DeviceDetails())));

        // Safely set currency from search criteria
        String currency = searchHotelsRequestGateway.getSearchCriteria()!=null && StringUtils.isNotEmpty(searchHotelsRequestGateway.getSearchCriteria().getCurrency()) ? searchHotelsRequestGateway.getSearchCriteria().getCurrency().toUpperCase() : "INR";
        requestDetails.setCurrency(Currency.valueOf(currency));
        requestDetails.setOptedTickTock(searchHotelsRequestGateway.getRequestDetails() != null && searchHotelsRequestGateway.getRequestDetails().isTickTockDealApplied());


        // RequestDetails - Safely retrieve values from requestDetails, setting defaults if necessary
        RequestDetails requestDetailsFromGateway = Optional.ofNullable(searchHotelsRequestGateway.getRequestDetails())
                .orElse(new RequestDetails());

        requestDetails.setRequestId(Optional.ofNullable(MDC.get(MDCHelper.MDCKeys.CORRELATION.getStringValue())).orElse(""));
        requestDetails.setJourneyId(Optional.ofNullable(requestDetailsFromGateway.getJourneyId()).orElse(""));
        requestDetails.setFunnelSource(Optional.ofNullable(requestDetailsFromGateway.getFunnelSource())
                .map(Funnel::fromValue)
                .orElse(Funnel.HOTELS));  // Default to a "Hotels" value for Funnel

        requestDetails.setPageContext(Optional.ofNullable(requestDetailsFromGateway.getPageContext())
                .map(PageContext::fromValue)
                .orElse(PageContext.LISTING));  // Defaulting to GUEST page context if not provided

        requestDetails.setVisitorId(Optional.ofNullable(requestDetailsFromGateway.getVisitorId()).orElse("")); // Safe visitorId

        // Traffic Source and Type - Safely set from requestDetails
        requestDetails.setTrafficType(Optional.ofNullable(requestDetailsFromGateway.getTrafficSource())
                .map(com.mmt.hotels.clientgateway.request.TrafficSource::getType)
                .map(TrafficType::fromValue)
                .orElse(TrafficType.B2C));  // Default to B2C if not present

        requestDetails.setTrafficSource(Optional.ofNullable(requestDetailsFromGateway.getTrafficSource())
                .map(com.mmt.hotels.clientgateway.request.TrafficSource::getSource)
                .orElse(TrafficSource.DEFAULT.getName()));  // Defaulting to DEFAULT traffic source

        requestDetails.setBrand(Optional.ofNullable(requestDetailsFromGateway.getBrand())
                .map(Brand::valueOf)
                .orElse(Brand.GI));  // Default to GI brand

        requestDetails.setIdContext(Optional.ofNullable(requestDetailsFromGateway.getIdContext())
                .map(IdContext::valueOf)
                .orElse(IdContext.B2C));  // Default to B2C context

        requestDetails.setSiteDomain(Optional.ofNullable(requestDetailsFromGateway.getSiteDomain())
                .map(String::toUpperCase)
                .map(SiteDomain::valueOf)
                .orElse(SiteDomain.IN));  // Default to IN domain

        Language language = Optional.of(MDC.get(MDCHelper.MDCKeys.LANGUAGE.getStringValue()))
                .map(String::toLowerCase)
                .map(Language::fromValue)
                .orElse(Language.ENGLISH);

        requestDetails.setLanguage(language);

        Region region = Optional.of(MDC.get(MDCHelper.MDCKeys.REGION.getStringValue()))
                .map(String::toLowerCase)
                .map(Region::fromValue)
                .orElse(Region.IN);

        requestDetails.setRegion(region);

        boolean ihCall = !Constants.DOM_COUNTRY.equalsIgnoreCase(searchHotelsRequestGateway.getSearchCriteria().getCountryCode());
        requestDetails.setCountry(ihCall ? Country.IH : Country.DH);


        requestDetails.setChannel(Optional.ofNullable(requestDetailsFromGateway.getChannel()).orElse(""));  // Safely set channel

        // Set MatchMakerRequest safely
//        MatchMakerRequest matchMakerDetails = Optional.ofNullable(searchHotelsRequestGateway.getMatchMakerDetails()).orElse(new MatchMakerRequest());
//        requestDetails.setMatchMakerRequest(buildMatchmakerRequestObject(matchMakerDetails));

        // Affiliate, ApplicationId, and other safe values
        requestDetails.setAffiliateId(Optional.ofNullable(commonModifierResponse.getAffiliateId()).orElse(""));
        requestDetails.setApplicationId(String.valueOf(Optional.of(commonModifierResponse.getApplicationId()).orElse(0)));
        requestDetails.setRequestType("B2CAgent"); // Hardcoded as per current implementation
        requestDetails.setCdfContextId(Optional.ofNullable(commonModifierResponse.getCdfContextId()).orElse(""));

        // TODO: Duplicate handling or necessary fields to be handled as needed
        // requestDetails.setContentExpDataMap(Collections.emptyMap());
        // requestDetails.setManthanExpDataMap(Collections.emptyMap());

        // Hardcoded or static values not used in listing
        // requestDetails.setBnplInfo(new BNPLInfo());  // Not used in Listing Page

        requestDetails.setB2bAttributes(Collections.emptyMap());

        // Set SEO-related fields
        requestDetails.setSeoCohort(Optional.ofNullable(searchHotelsRequestGateway.getFeatureFlags()).map(FeatureFlags::getSeoCohort).orElse(""));
        if(searchHotelsRequestGateway != null && searchHotelsRequestGateway.getRequestDetails()!=null) {
            requestDetails.setRequestor(searchHotelsRequestGateway.getRequestDetails().getRequestor());
        }
        requestDetails.setUserPreferredCouponCode(requestDetailsFromGateway.getUserPreferredCouponCode());

        LOGGER.info("Successfully built RequestDetails.");
        return requestDetails;
    }

    private BookingDevice buildBookingDevice(DeviceDetails deviceDetails) {
        if (deviceDetails == null) {
            LOGGER.warn("DeviceDetails is null, returning an empty BookingDevice.");
            return BookingDevice.builder().build();  // Return an empty BookingDevice if deviceDetails is null
        }

        BookingDevice.BookingDeviceBuilder bookingDeviceBuilder = BookingDevice.builder();

        // Safely set device fields, defaulting to empty strings or enums if values are null
        bookingDeviceBuilder.deviceId(Optional.ofNullable(deviceDetails.getDeviceId()).orElse(""));
        bookingDeviceBuilder.deviceName(Optional.ofNullable(deviceDetails.getDeviceName()).orElse(""));

        DeviceType deviceType = Optional.of(deviceDetails.getBookingDevice())
                .map(DeviceType::fromValue)
                .orElse(DeviceType.DESKTOP);
        bookingDeviceBuilder.deviceType(deviceType);

        bookingDeviceBuilder.appVersion(Optional.ofNullable(deviceDetails.getAppVersion()).orElse(""));
        bookingDeviceBuilder.networkType(Optional.ofNullable(deviceDetails.getNetworkType()).orElse(""));
//        bookingDeviceBuilder.version(deviceDetails.getAppVersion());
//        bookingDeviceBuilder.os(deviceDetails.getDe());
        LOGGER.info("Successfully built BookingDevice with deviceId: {}", deviceDetails.getDeviceId());
        return bookingDeviceBuilder.build();
    }


    private UserDetails buildUserDetails(ListingSearchRequest searchHotelsRequestGateway, CommonModifierResponse commonModifierResponse) {
        if (searchHotelsRequestGateway == null) {
            LOGGER.warn("ListingSearchRequest is null while building UserDetails, returning empty UserDetails.");
            return new UserDetails();  // Return empty UserDetails if searchHotelsRequestGateway is null
        }

        UserDetails userDetails = new UserDetails();

        // Safely set the location details using LocationDetails builder
        LocationDetails locationDetails = LocationDetails.builder()
                .cityName(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if city is null
                .cityId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCity)
                        .orElse(""))  // Default to empty string if cityId is null
                .countryId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getCountry)
                        .orElse(""))  // Default to empty string if countryId is null
                .stateId(Optional.ofNullable(commonModifierResponse.getUserLocation())
                        .map(UserLocation::getState)
                        .orElse(""))  // Default to empty string if stateId is null
                .build();
        userDetails.setLocation(locationDetails);

        // Safely set mmtAuth, uuid, profileType, and subProfileType with default values if null
        userDetails.setMmtAuth(Optional.ofNullable(commonModifierResponse.getMmtAuth()).orElse(""));
        userDetails.setUuid(Optional.ofNullable(commonModifierResponse.getExtendedUser()).map(ExtendedUser::getUuid).orElse(""));

        // Safely map profileType and subProfileType, defaulting to UNKNOWN or empty if null
        userDetails.setProfileType(Optional.ofNullable(commonModifierResponse.getExtendedUser())
                .map(ExtendedUser::getProfileType)
                .map(ProfileType::valueOf)
                .orElse(ProfileType.PERSONAL));  // Default to UNKNOWN if profileType is null

        userDetails.setSubProfileType(Optional.ofNullable(commonModifierResponse.getExtendedUser())
                .map(ExtendedUser::getAffiliateId)
                .map(SubProfileType::fromValue)
                .orElse(SubProfileType.DEFAULT));
        // Default to DEFAULT if subProfileType is null

        // Safely set loggedIn, defaulting to false if null
        userDetails.setLoggedIn(Optional.ofNullable(searchHotelsRequestGateway.getRequestDetails())
                .map(RequestDetails::isLoggedIn)
                .orElse(false));

        // Safely set user segments
        List<String> userSegmentsList = new ArrayList<>(Optional.ofNullable(commonModifierResponse.getHydraResponse())
                .map(HydraResponse::getHydraMatchedSegment)
                .orElse(Collections.emptySet()));
        userDetails.setUserSegments(userSegmentsList);
        LOGGER.info("Successfully built UserDetails for UUID: {}", userDetails.getUuid());
        return userDetails;
    }

}