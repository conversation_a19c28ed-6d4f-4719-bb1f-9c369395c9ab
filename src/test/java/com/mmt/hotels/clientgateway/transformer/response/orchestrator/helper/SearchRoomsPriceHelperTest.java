package com.mmt.hotels.clientgateway.transformer.response.orchestrator.helper;

import com.gommt.hotels.orchestrator.detail.model.response.da.OccupancyDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.DiscountDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.ExtraDiscount;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.LinkedRatePriceCalculations;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.NoCostEmiDetails;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceCouponInfo;
import com.gommt.hotels.orchestrator.detail.model.response.pricing.PriceDetail;
//import com.gommt.hotels.orchestrator.detail.model.response.pricing.TaxBreakUp;
import com.ibm.icu.text.NumberFormat;
import com.mmt.hotels.clientgateway.businessobjects.CommonModifierResponse;
import com.mmt.hotels.clientgateway.response.EMIDetail;
import com.mmt.hotels.clientgateway.response.PricingDetails;
import com.mmt.hotels.clientgateway.response.TotalPricing;
import com.mmt.hotels.clientgateway.service.PolyglotService;
import com.mmt.hotels.clientgateway.transformer.response.CommonResponseTransformer;
import com.mmt.hotels.clientgateway.util.MDCHelper;
import com.mmt.hotels.clientgateway.util.Utility;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SearchRoomsPriceHelper in GI project
 * Tests the actual GI implementation with its simpler method signatures and functionality
 */
@RunWith(MockitoJUnitRunner.class)
public class SearchRoomsPriceHelperTest {

    @Mock
    private CommonResponseTransformer commonResponseTransformer;

    @Mock
    private PolyglotService polyglotService;

    @Mock
    private Utility utility;

    @InjectMocks
    private SearchRoomsPriceHelper searchRoomsPriceHelper;

    @Before
    public void setUp() {
        // Initialize the NumberFormat manually since @PostConstruct won't work in unit tests
        NumberFormat numberFormatter = NumberFormat.getNumberInstance(new Locale("en", "IN"));
        numberFormatter.setMaximumFractionDigits(0);
        numberFormatter.setMinimumFractionDigits(0);
        ReflectionTestUtils.setField(searchRoomsPriceHelper, "numberFormatter", numberFormatter);

        // Setup common mock behaviors
//        when(utility.getExpDataMap(anyString())).thenReturn(new LinkedHashMap<>());
//        when(utility.isExperimentOn(any(Map.class), anyString())).thenReturn(false);
        when(commonResponseTransformer.getPriceDisplayMessage(any(Map.class), any(), anyString(), any(), anyBoolean(), anyString())).thenReturn("Per Night");
        when(commonResponseTransformer.getShowTaxMessage(any(Map.class), any(), anyDouble(), anyString())).thenReturn("Taxes Included");
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
//        when(commonResponseTransformer.getGroupPriceText(any(Integer.class), any(Integer.class))).thenReturn("Group Price Text");
        when(commonResponseTransformer.getGroupPriceText(any(Integer.class), any(Integer.class), anyString(), anyString())).thenReturn("Group Price Text with Amount");
    }

    @Test
    public void init_ShouldInitializeNumberFormatter() {
        // When
        searchRoomsPriceHelper.init();

        // Then
        // No exception should be thrown and method should complete successfully
    }

    // ==================== getPriceMap Tests ====================

    @Test
    public void should_ReturnNull_When_PriceDetailIsNull() {
        // Given
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                null, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNull(result);
    }

    @Test
    public void should_GetPriceMap_When_ValidPriceDetailWithCouponCode() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setCouponCode("TEST_COUPON");
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", true, false, false, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNotNull(totalPricing);
        assertEquals("TEST_PRICING_KEY", totalPricing.getPricingKey());
        assertEquals("Per Night", totalPricing.getPriceDisplayMsg());
        assertEquals("Taxes Included", totalPricing.getPriceTaxMsg());
    }

    @Test
    public void should_GetPriceMap_When_ValidPriceDetailWithoutCouponCode() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setCouponCode(null);
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("DEFAULT"));
        TotalPricing totalPricing = result.get("DEFAULT");
        assertNotNull(totalPricing);
    }

    @Test
    public void should_GetPriceMap_When_NullOccupancyDetails() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        Map<String, String> expDataMap = new LinkedHashMap<>();

//        when(commonResponseTransformer.getPriceDisplayMessage(any(Map.class), isNull(), anyString(), any(), anyBoolean(), anyString())).thenReturn("Per Night");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, null, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
    }

    @Test
    public void should_GetPriceMap_When_ApplicableCouponsProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        List<PriceCouponInfo> applicableCoupons = new ArrayList<>();
        PriceCouponInfo coupon1 = new PriceCouponInfo();
        coupon1.setCouponCode("TEST_COUPON");
        coupon1.setDescription("Test Coupon Description");
        coupon1.setDiscount(500.0);
        applicableCoupons.add(coupon1);
        
        PriceCouponInfo coupon2 = new PriceCouponInfo();
        coupon2.setCouponCode("EXTRA_COUPON");
        coupon2.setDescription("Extra Coupon Description");
        coupon2.setDiscount(300.0);
        applicableCoupons.add(coupon2);
        
        priceDetail.setApplicableCoupons(applicableCoupons);
        
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
        assertTrue(result.containsKey("EXTRA_COUPON"));
        
        TotalPricing mainCouponPricing = result.get("TEST_COUPON");
        assertEquals("Test Coupon Description", mainCouponPricing.getCouponDesc());
        assertEquals(500.0, mainCouponPricing.getCouponAmount(), 0);
        
        TotalPricing extraCouponPricing = result.get("EXTRA_COUPON");
        assertEquals("Extra Coupon Description", extraCouponPricing.getCouponDesc());
        assertEquals(300.0, extraCouponPricing.getCouponAmount(), 0);
    }

//    @Test
//    public void should_GetPriceMap_When_GroupBookingFunnelEnabled() {
//        // Given
//        PriceDetail priceDetail = createValidPriceDetail();
//        priceDetail.setBasePrice(2000.0);
//        priceDetail.setTotalDiscount(400.0); // 20% saving
//
//        Map<String, String> expDataMap = new LinkedHashMap<>();
//        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
//
//        when(polyglotService.getTranslatedData(anyString())).thenReturn("Save {PERCENTAGE}%");
//
//        // When
//        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
//                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
//                false, "DEFAULT", false, true, true, false, "listing");
//
//        // Then
//        assertNotNull(result);
//        verify(commonResponseTransformer, atLeastOnce()).getGroupPriceText(any(Integer.class), any(Integer.class), anyString(), anyString());
//    }

    @Test
    public void should_GetPriceMap_When_BuildToolTipEnabled() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", true, false, false, false, "listing");

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        // Should have price tooltip set (method internally calls buildPriceToolTipFromPriceDetail)
        assertNotNull(totalPricing);
    }

    @Test
    public void should_GetPriceMap_When_CorpRequest() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                true, "CORP_SEGMENT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
    }

//    @Test
//    public void should_GetPriceMap_When_MyPartnerRequest() {
//        // Given
//        PriceDetail priceDetail = createValidPriceDetail();
//        Map<String, String> expDataMap = new LinkedHashMap<>();
//        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
//
//        when(utility.isDetailPageAPI(anyString())).thenReturn(true);
//
//        // Mock MDC for controller
//        MDC.put(MDCHelper.MDCKeys.CONTROLLER.getStringValue(), "detail-page");
//
//        // When
//        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
//                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
//                false, "DEFAULT", false, true, true, true, "listing");
//
//        // Then
//        assertNotNull(result);
//        verify(commonResponseTransformer).getGroupPriceText(any(Integer.class), any(Integer.class));
//
//        // Clean up MDC
//        MDC.clear();
//    }

    @Test
    public void should_BuildLinkedRatesPersuasions_When_LinkedRatePriceCalculationsMapProvided() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        Map<String, LinkedRatePriceCalculations> linkedRateMap = new HashMap<>();
        LinkedRatePriceCalculations calculations = new LinkedRatePriceCalculations();
        calculations.setDisplayPriceDifference(500);
        calculations.setParentOriginalPrice(3000);
        linkedRateMap.put("LINKED_RATE_1", calculations);
        priceDetail.setLinkedRatePriceCalculationsMap(linkedRateMap);
        
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        when(polyglotService.getTranslatedData("GI_LINKED_RATE_PLAN_BOTTOMSHEET_TITLE")).thenReturn("Save {discount} on this rate!");
        when(polyglotService.getTranslatedData("GI_LINKED_RATE_PLAN_DISCOUNT_TEXT")).thenReturn("You save {discount}");
        when(polyglotService.getTranslatedData("GI_LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT")).thenReturn("Original price was {parentOriginalPrice}");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNotNull(totalPricing.getLinkedRPBottomSheetTitle());
        assertNotNull(totalPricing.getLinkedRPDiscountMsg());
        assertNotNull(totalPricing.getLinkedRPOriginalPriceMsg());
        
        verify(polyglotService).getTranslatedData("GI_LINKED_RATE_PLAN_BOTTOMSHEET_TITLE");
        verify(polyglotService).getTranslatedData("GI_LINKED_RATE_PLAN_DISCOUNT_TEXT");
        verify(polyglotService).getTranslatedData("GI_LINKED_RATE_PLAN_ORIGINAL_PRICE_TEXT");
    }

    @Test
    public void should_HandleEmptyLinkedRatePriceCalculationsMap_When_MapIsEmpty() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setLinkedRatePriceCalculationsMap(new HashMap<>());
        
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNull(totalPricing.getLinkedRPBottomSheetTitle());
        assertNull(totalPricing.getLinkedRPDiscountMsg());
        assertNull(totalPricing.getLinkedRPOriginalPriceMsg());
    }

    // ==================== buildPriceToolTipFromPriceDetail Tests ====================

    @Test
    public void should_ReturnNull_When_BuildPriceToolTipWithNullPriceDetail() {
        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(null, 2, "INR");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildPriceToolTipWithNullNightCount() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();

        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, null, "INR");

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildPriceToolTipWithZeroNightCount() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();

        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 0, "INR");

        // Then
        assertNull(result);
    }

//    @Test
//    public void should_BuildPriceToolTip_When_ValidPriceDetailWithBasePriceAndTax() {
//        // Given
//        PriceDetail priceDetail = createValidPriceDetail();
//        priceDetail.setBasePrice(2000.0);
//        priceDetail.setTotalTax(400.0);
//        priceDetail.setTotalDiscount(200.0);
//
//        when(polyglotService.getTranslatedData("BASE_FARE_LABEL")).thenReturn("Base Fare");
//        when(polyglotService.getTranslatedData("TAX_AND_SERVICE_FEE")).thenReturn("Taxes & Service Fee");
//        when(polyglotService.getTranslatedData("TOTAL_DISCOUNT_LABEL")).thenReturn("Total Discount");
//
//        // When
//        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 2, "INR");
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("Base Fare"));
//        assertTrue(result.contains("Taxes & Service Fee"));
//        assertTrue(result.contains("Total Discount"));
//        assertTrue(result.contains("₹"));
//        assertTrue(result.contains("x 2 nights"));
//    }
//
//    @Test
//    public void should_BuildPriceToolTip_When_OnlyBasePriceAvailable() {
//        // Given
//        PriceDetail priceDetail = createValidPriceDetail();
//        priceDetail.setBasePrice(2000.0);
//        priceDetail.setTotalTax(0.0);
//        priceDetail.setTotalDiscount(0.0);
//
//        when(polyglotService.getTranslatedData("BASE_FARE_LABEL")).thenReturn("Base Fare");
//
//        // When
//        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 2, "INR");
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("Base Fare"));
//        assertFalse(result.contains("Taxes & Service Fee"));
//        assertFalse(result.contains("Total Discount"));
//    }

    @Test
    public void should_BuildPriceToolTip_When_OnlyTaxAvailable() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        priceDetail.setBasePrice(0.0);
        priceDetail.setTotalTax(400.0);
        priceDetail.setTotalDiscount(0.0);

        when(polyglotService.getTranslatedData("TAX_AND_SERVICE_FEE")).thenReturn("Taxes & Service Fee");

        // When
        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 2, "INR");

        // Then
        assertNotNull(result);
        assertTrue(result.contains("Taxes & Service Fee"));
        assertFalse(result.contains("Base Fare"));
        assertFalse(result.contains("Total Discount"));
    }

//    @Test
//    public void should_BuildPriceToolTip_When_OnlyDiscountAvailable() {
//        // Given
//        PriceDetail priceDetail = createValidPriceDetail();
//        priceDetail.setBasePrice(0.0);
//        priceDetail.setTotalTax(0.0);
//        priceDetail.setTotalDiscount(200.0);
//
//        when(polyglotService.getTranslatedData("TOTAL_DISCOUNT_LABEL")).thenReturn("Total Discount");
//
//        // When
//        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 2, "INR");
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("Total Discount"));
//        assertFalse(result.contains("Base Fare"));
//        assertFalse(result.contains("Taxes & Service Fee"));
//    }
//
//    @Test
//    public void should_BuildPriceToolTip_When_DifferentCurrency() {
//        // Given
//        PriceDetail priceDetail = createValidPriceDetail();
//        priceDetail.setBasePrice(2000.0);
//
//        when(polyglotService.getTranslatedData("BASE_FARE_LABEL")).thenReturn("Base Fare");
//
//        // When
//        String result = searchRoomsPriceHelper.buildPriceToolTipFromPriceDetail(priceDetail, 3, "USD");
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.contains("Base Fare"));
//        assertTrue(result.contains("$"));
//        assertTrue(result.contains("x 3 nights"));
//    }

    // ==================== buildEmiBankDetails Tests ====================

    @Test
    public void should_ReturnNull_When_BuildEmiBankDetailsWithNullInput() {
        // When
        EMIDetail result = searchRoomsPriceHelper.buildEmiBankDetails(null);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_BuildEmiBankDetailsWithZeroEmiAmount() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(0.0);
        noCostEmiDetails.setBankName("TEST_BANK");
        noCostEmiDetails.setTenure(12);

        // When
        EMIDetail result = searchRoomsPriceHelper.buildEmiBankDetails(noCostEmiDetails);

        // Then
        assertNull(result);
    }

    @Test
    public void should_BuildEmiBankDetails_When_ValidNoCostEmiDetailsProvided() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(1000.0);
        noCostEmiDetails.setBankName("HDFC_BANK");
        noCostEmiDetails.setTenure(12);

        // When
        EMIDetail result = searchRoomsPriceHelper.buildEmiBankDetails(noCostEmiDetails);

        // Then
        assertNotNull(result);
        assertEquals(1000.0, result.getAmount(), 0);
        assertEquals("HDFC_BANK", result.getBankName());
        assertEquals(12, result.getTenure());
    }

    @Test
    public void should_BuildEmiBankDetails_When_DifferentBankAndTenure() {
        // Given
        NoCostEmiDetails noCostEmiDetails = new NoCostEmiDetails();
        noCostEmiDetails.setEmiAmount(750.0);
        noCostEmiDetails.setBankName("ICICI_BANK");
        noCostEmiDetails.setTenure(6);

        // When
        EMIDetail result = searchRoomsPriceHelper.buildEmiBankDetails(noCostEmiDetails);

        // Then
        assertNotNull(result);
        assertEquals(750.0, result.getAmount(), 0);
        assertEquals("ICICI_BANK", result.getBankName());
        assertEquals(6, result.getTenure());
    }

    // ==================== convertNumericValueToCommaSeparatedString Tests ====================

    @Test
    public void should_ConvertNumericValue_When_IndianLocale() {
        // When
        String result = searchRoomsPriceHelper.convertNumericValueToCommaSeparatedString(1000, Locale.ENGLISH);

        // Then
        assertEquals("1,000", result);
    }

    @Test
    public void should_ConvertNumericValue_When_LargeNumber() {
        // When
        String result = searchRoomsPriceHelper.convertNumericValueToCommaSeparatedString(1234567, Locale.ENGLISH);

        // Then
        assertEquals("1,234,567", result);
    }

    @Test
    public void should_ConvertNumericValue_When_SmallNumber() {
        // When
        String result = searchRoomsPriceHelper.convertNumericValueToCommaSeparatedString(100, Locale.ENGLISH);

        // Then
        assertEquals("100", result);
    }

    @Test
    public void should_ConvertNumericValue_When_ZeroValue() {
        // When
        String result = searchRoomsPriceHelper.convertNumericValueToCommaSeparatedString(0, Locale.ENGLISH);

        // Then
        assertEquals("0", result);
    }

    @Test
    public void should_ConvertNumericValue_When_DifferentLocale() {
        // When
        String result = searchRoomsPriceHelper.convertNumericValueToCommaSeparatedString(1000, Locale.FRANCE);

        // Then
        assertNotNull(result);
        // French locale uses different number formatting
    }

    // ==================== getOffersAppliedText Tests ====================

//    @Test
//    public void should_ReturnCashbackAppliedText_When_GocashVariant3AndHybridDiscount() {
//        // Given
//        List<PricingDetails> pricingDetails = new ArrayList<>();
//        Map<String, String> expData = new LinkedHashMap<>();
//        expData.put("recomFlow", "false");
//        String currency = "INR";
//
//        DiscountDetails discountDetails = new DiscountDetails();
//        discountDetails.setHybrid(100.0);
//
//        when(Utility.gocashVariant3(expData, true)).thenReturn(true);
//
//        // When
//        String result = searchRoomsPriceHelper.getOffersAppliedText(pricingDetails, expData, currency, discountDetails);
//
//        // Then
//        assertEquals("OFFERS + CASHBACK APPLIED", result);
//    }
//
//    @Test
//    public void should_ReturnOffersAppliedText_When_TotalDiscountWithBreakup() {
//        // Given
//        List<PricingDetails> pricingDetails = new ArrayList<>();
//
//        PricingDetails totalDiscount = new PricingDetails();
//        totalDiscount.setKey("TOTAL_DISCOUNT_KEY");
//
//        List<PricingDetails> breakup = new ArrayList<>();
//        breakup.add(new PricingDetails());
//        breakup.add(new PricingDetails());
//        totalDiscount.setBreakup(breakup);
//
//        pricingDetails.add(totalDiscount);
//
//        Map<String, String> expData = new LinkedHashMap<>();
//        String currency = "INR";
//        DiscountDetails discountDetails = new DiscountDetails();
//
//        when(Utility.gocashVariant3(expData, true)).thenReturn(false);
//
//        // When
//        String result = searchRoomsPriceHelper.getOffersAppliedText(pricingDetails, expData, currency, discountDetails);
//
//        // Then
//        assertEquals("2 OFFERS APPLIED", result);
//    }
//
//    @Test
//    public void should_ReturnEmptyString_When_NoOffersApplied() {
//        // Given
//        List<PricingDetails> pricingDetails = new ArrayList<>();
//        Map<String, String> expData = new LinkedHashMap<>();
//        String currency = "INR";
//        DiscountDetails discountDetails = new DiscountDetails();
//
//        when(Utility.gocashVariant3(expData, true)).thenReturn(false);
//
//        // When
//        String result = searchRoomsPriceHelper.getOffersAppliedText(pricingDetails, expData, currency, discountDetails);
//
//        // Then
//        assertEquals("", result);
//    }

    // ==================== Integration Tests ====================

    @Test
    public void should_HandleCompleteScenario_When_AllParametersProvided() {
        // Given
        PriceDetail priceDetail = createComplexPriceDetail();
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();
        
        when(polyglotService.getTranslatedData(anyString())).thenReturn("Translated Text");
        when(commonResponseTransformer.getShowTaxMessage(any(Map.class), any(), anyDouble(), anyString())).thenReturn("Includes all taxes");
        
        // Mock MDC for B2C context
        MDC.put(MDCHelper.MDCKeys.IDCONTEXT.getStringValue(), "B2C");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", true, true, true, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("TEST_COUPON"));
        assertTrue(result.containsKey("EXTRA_COUPON"));
        
        TotalPricing mainTotalPricing = result.get("TEST_COUPON");
        assertNotNull(mainTotalPricing);
        assertNotNull(mainTotalPricing.getDetails());
        assertFalse(mainTotalPricing.getDetails().isEmpty());
        assertEquals("Test Coupon Description", mainTotalPricing.getCouponDesc());
        assertEquals(500.0, mainTotalPricing.getCouponAmount(), 0);
        
        // Verify various method calls
        verify(commonResponseTransformer, atLeastOnce()).getPriceDisplayMessage(any(Map.class), any(), anyString(), any(), anyBoolean(), anyString());
        verify(commonResponseTransformer, atLeastOnce()).getShowTaxMessage(any(Map.class), any(), anyDouble(), anyString());
        verify(polyglotService, atLeastOnce()).getTranslatedData(anyString());
        
        // Clean up MDC
        MDC.clear();
    }

    @Test
    public void should_HandleEdgeCases_When_MinimalValidInput() {
        // Given
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setDisplayPrice(1000.0);
        priceDetail.setDiscount(new DiscountDetails());
        
        Map<String, String> expDataMap = new LinkedHashMap<>();
        
        when(polyglotService.getTranslatedData(anyString())).thenReturn("No Coupons Available");

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, null, "INR", "ROOM", 1,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        assertTrue(result.containsKey("DEFAULT"));
        
        TotalPricing totalPricing = result.get("DEFAULT");
        assertNotNull(totalPricing);
        assertEquals("No Coupons Available", totalPricing.getNoCouponText());
    }

    @Test
    public void should_HandleExceptionGracefully_When_LinkedRatePersuasionBuilding() {
        // Given
        PriceDetail priceDetail = createValidPriceDetail();
        
        // Create a problematic linked rate map that might cause issues
        Map<String, LinkedRatePriceCalculations> linkedRateMap = new HashMap<>();
        linkedRateMap.put("INVALID_RATE", null); // null value that might cause NPE
        priceDetail.setLinkedRatePriceCalculationsMap(linkedRateMap);
        
        Map<String, String> expDataMap = new LinkedHashMap<>();
        OccupancyDetails occupancyDetails = createValidOccupancyDetails();

        // When
        Map<String, TotalPricing> result = searchRoomsPriceHelper.getPriceMap(
                priceDetail, expDataMap, occupancyDetails, "INR", "ROOM", 2,
                false, "DEFAULT", false, false, false, false, "listing");

        // Then
        assertNotNull(result);
        // Should not throw exception and should handle gracefully
        TotalPricing totalPricing = result.get("TEST_COUPON");
        assertNotNull(totalPricing);
    }

    // ==================== Helper Methods for Test Data Creation ====================

    private PriceDetail createValidPriceDetail() {
        PriceDetail priceDetail = new PriceDetail();
        priceDetail.setCouponCode("TEST_COUPON");
        priceDetail.setDisplayPrice(5000.0);
        priceDetail.setBasePrice(4000.0);
        priceDetail.setTotalTax(1000.0);
        priceDetail.setTotalDiscount(200.0);
        priceDetail.setPricingKey("TEST_PRICING_KEY");
        
        DiscountDetails discount = new DiscountDetails();
        discount.setCoupon(100.0);
        discount.setSupplier(50.0);
        discount.setMmt(100.0);
        discount.setBlack(50.0);
        priceDetail.setDiscount(discount);
        
//        TaxBreakUp taxBreakUp = new TaxBreakUp();
//        taxBreakUp.setHotelTax(800.0);
//        taxBreakUp.setServiceFee(200.0);
//        taxBreakUp.setHotelServiceCharge(100.0);
//        priceDetail.setTaxBreakUp(taxBreakUp);
        
        return priceDetail;
    }

    private PriceDetail createComplexPriceDetail() {
        PriceDetail priceDetail = createValidPriceDetail();
        
        // Add applicable coupons
        List<PriceCouponInfo> applicableCoupons = new ArrayList<>();
        
        PriceCouponInfo coupon1 = new PriceCouponInfo();
        coupon1.setCouponCode("TEST_COUPON");
        coupon1.setDescription("Test Coupon Description");
        coupon1.setDiscount(500.0);
        coupon1.setBnplAllowed(true);
        coupon1.setNoCostEmiApplicable(true);
        coupon1.setPromoIconLink("http://example.com/icon.png");
        coupon1.setBankOffer(false);
        coupon1.setTncUrl("http://example.com/tnc");
        coupon1.setSubDescription("Test Sub Description");
        coupon1.setCtaBankText("Apply Now");
        coupon1.setCtaBankUrl("http://example.com/apply");
        applicableCoupons.add(coupon1);
        
        PriceCouponInfo coupon2 = new PriceCouponInfo();
        coupon2.setCouponCode("EXTRA_COUPON");
        coupon2.setDescription("Extra Coupon Description");
        coupon2.setDiscount(300.0);
        coupon2.setBnplAllowed(false);
        coupon2.setNoCostEmiApplicable(false);
        coupon2.setBankOffer(true);
        applicableCoupons.add(coupon2);
        
        priceDetail.setApplicableCoupons(applicableCoupons);
        
        // Add extra discount
        ExtraDiscount extraDiscount = new ExtraDiscount();
        extraDiscount.setType("FIRST_BOOKING");
        extraDiscount.setDiscount(200.0);
        extraDiscount.setBookingCount(1);
        priceDetail.setExtraDiscount(extraDiscount);
        
        // Add linked rate calculations
        Map<String, LinkedRatePriceCalculations> linkedRateMap = new HashMap<>();
        LinkedRatePriceCalculations calculations = new LinkedRatePriceCalculations();
        calculations.setDisplayPriceDifference(500);
        calculations.setParentOriginalPrice(3000);
        linkedRateMap.put("LINKED_RATE_1", calculations);
        priceDetail.setLinkedRatePriceCalculationsMap(linkedRateMap);
        
        return priceDetail;
    }

    private OccupancyDetails createValidOccupancyDetails() {
        OccupancyDetails occupancyDetails = new OccupancyDetails();
        occupancyDetails.setNumberOfRooms(2);
        occupancyDetails.setAdult(2);
        occupancyDetails.setChild(1);
        occupancyDetails.setPricingKey("TEST_PRICING_KEY");
        return occupancyDetails;
    }

    // ==================== buildLinkedRatePriceCalculations Tests ====================

    @Test
    public void should_ReturnNull_When_LinkedRatePriceCalculationsMapIsNull() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = null;

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ReturnNull_When_LinkedRatePriceCalculationsMapIsEmpty() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = new HashMap<>();

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNull(result);
    }

    @Test
    public void should_ConvertLinkedRatePriceCalculations_When_ValidInputProvided() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = new HashMap<>();
        
        LinkedRatePriceCalculations orchCalculations = new LinkedRatePriceCalculations();
        orchCalculations.setDisplayPriceDifference(500);
        orchCalculations.setParentOriginalPrice(2000);
        
        input.put("PARENT_RATE_1", orchCalculations);

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("PARENT_RATE_1"));
        
        com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations entityCalculations = result.get("PARENT_RATE_1");
        assertNotNull(entityCalculations);
        assertEquals(500, entityCalculations.getDisplayPriceDifference());
        assertEquals(2000, entityCalculations.getParentOriginalPrice());
    }

    @Test
    public void should_ConvertMultipleLinkedRatePriceCalculations_When_MultipleEntriesProvided() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = new HashMap<>();
        
        LinkedRatePriceCalculations orchCalculations1 = new LinkedRatePriceCalculations();
        orchCalculations1.setDisplayPriceDifference(500);
        orchCalculations1.setParentOriginalPrice(2000);
        input.put("PARENT_RATE_1", orchCalculations1);
        
        LinkedRatePriceCalculations orchCalculations2 = new LinkedRatePriceCalculations();
        orchCalculations2.setDisplayPriceDifference(300);
        orchCalculations2.setParentOriginalPrice(1800);
        input.put("PARENT_RATE_2", orchCalculations2);

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("PARENT_RATE_1"));
        assertTrue(result.containsKey("PARENT_RATE_2"));
        
        com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations entityCalculations1 = result.get("PARENT_RATE_1");
        assertEquals(500, entityCalculations1.getDisplayPriceDifference());
        assertEquals(2000, entityCalculations1.getParentOriginalPrice());
        
        com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations entityCalculations2 = result.get("PARENT_RATE_2");
        assertEquals(300, entityCalculations2.getDisplayPriceDifference());
        assertEquals(1800, entityCalculations2.getParentOriginalPrice());
    }

    @Test
    public void should_HandleNullValues_When_OrchCalculationsHasNullFields() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = new HashMap<>();
        
        LinkedRatePriceCalculations orchCalculations = new LinkedRatePriceCalculations();
        orchCalculations.setDisplayPriceDifference(null);
        orchCalculations.setParentOriginalPrice(null);
        
        input.put("PARENT_RATE_1", orchCalculations);

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        
        com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations entityCalculations = result.get("PARENT_RATE_1");
        assertNotNull(entityCalculations);
        assertEquals(0, entityCalculations.getDisplayPriceDifference()); // Default value
        assertEquals(0, entityCalculations.getParentOriginalPrice()); // Default value
    }

    @Test
    public void should_SkipNullEntries_When_MapContainsNullValues() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = new HashMap<>();
        
        LinkedRatePriceCalculations orchCalculations = new LinkedRatePriceCalculations();
        orchCalculations.setDisplayPriceDifference(500);
        orchCalculations.setParentOriginalPrice(2000);
        
        input.put("PARENT_RATE_1", orchCalculations);
        input.put("PARENT_RATE_2", null); // Null value

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Only non-null entry should be processed
        assertTrue(result.containsKey("PARENT_RATE_1"));
        assertFalse(result.containsKey("PARENT_RATE_2"));
    }

    @Test
    public void should_ReturnNull_When_AllEntriesAreNull() throws Exception {
        // Given
        Map<String, LinkedRatePriceCalculations> input = new HashMap<>();
        input.put("PARENT_RATE_1", null);
        input.put("PARENT_RATE_2", null);

        // When
        Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations> result = 
            (Map<String, com.mmt.hotels.model.response.pricing.LinkedRatePriceCalculations>) 
            ReflectionTestUtils.invokeMethod(searchRoomsPriceHelper, "buildLinkedRatePriceCalculations", input);

        // Then
        assertNull(result); // Should return null when result map is empty
    }
} 