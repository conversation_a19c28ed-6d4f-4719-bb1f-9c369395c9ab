package com.mmt.hotels.clientgateway.transformer.factory;

import com.mmt.hotels.clientgateway.transformer.request.SmartFiltersRequestTransformer;
import com.mmt.hotels.clientgateway.transformer.request.android.SmartFiltersRequestTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.request.desktop.SmartFiltersRequestTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.request.ios.SmartFiltersRequestTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.request.pwa.SmartFiltersRequestTransformerPWA;
import com.mmt.hotels.clientgateway.transformer.response.SmartFiltersResponseTransformer;
import com.mmt.hotels.clientgateway.transformer.response.android.SmartFiltersResponseTransformerAndroid;
import com.mmt.hotels.clientgateway.transformer.response.desktop.SmartFiltersResponseTransformerDesktop;
import com.mmt.hotels.clientgateway.transformer.response.ios.SmartFiltersResponseTransformerIOS;
import com.mmt.hotels.clientgateway.transformer.response.pwa.SmartFiltersResponseTransformerPWA;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SmartFiltersFactoryTest {

    @InjectMocks
    private SmartFiltersFactory smartFiltersFactory;

    @Mock
    private SmartFiltersRequestTransformerPWA smartFiltersRequestTransformerPWA;

    @Mock
    private SmartFiltersRequestTransformerAndroid smartFiltersRequestTransformerAndroid;

    @Mock
    private SmartFiltersRequestTransformerDesktop smartFiltersRequestTransformerDesktop;

    @Mock
    private SmartFiltersRequestTransformerIOS smartFiltersRequestTransformerIOS;

    @Mock
    private SmartFiltersResponseTransformerPWA smartFiltersResponseTransformerPWA;

    @Mock
    private SmartFiltersResponseTransformerAndroid smartFiltersResponseTransformerAndroid;

    @Mock
    private SmartFiltersResponseTransformerDesktop smartFiltersResponseTransformerDesktop;

    @Mock
    private SmartFiltersResponseTransformerIOS smartFiltersResponseTransformerIOS;

    @Before
    public void setUp() {
        // No setup needed - the factory will use the injected mocks automatically
    }

    @Test
    public void testGetRequestServiceForPWA() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("PWA");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerPWA, result);
    }

    @Test
    public void testGetRequestServiceForAndroid() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("ANDROID");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerAndroid, result);
    }

    @Test
    public void testGetRequestServiceForDesktop() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("DESKTOP");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerDesktop, result);
    }

    @Test
    public void testGetRequestServiceForIOS() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("IOS");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerIOS, result);
    }

    @Test
    public void testGetRequestServiceForMSITE() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("MSITE");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerPWA, result);
    }

    @Test
    public void testGetRequestServiceForNullClient() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService(null);
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerDesktop, result);
    }

    @Test
    public void testGetRequestServiceForEmptyClient() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerDesktop, result);
    }

    @Test
    public void testGetRequestServiceForUnknownClient() {
        SmartFiltersRequestTransformer result = smartFiltersFactory.getRequestService("UNKNOWN");
        assertNotNull(result);
        assertSame(smartFiltersRequestTransformerDesktop, result);
    }

    @Test
    public void testGetResponseServiceForPWA() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("PWA");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerPWA, result);
    }

    @Test
    public void testGetResponseServiceForAndroid() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("ANDROID");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerAndroid, result);
    }

    @Test
    public void testGetResponseServiceForDesktop() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("DESKTOP");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseServiceForIOS() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("IOS");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerIOS, result);
    }

    @Test
    public void testGetResponseServiceForMSITE() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("MSITE");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerPWA, result);
    }

    @Test
    public void testGetResponseServiceForNullClient() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService(null);
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseServiceForEmptyClient() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerDesktop, result);
    }

    @Test
    public void testGetResponseServiceForUnknownClient() {
        SmartFiltersResponseTransformer result = smartFiltersFactory.getResponseService("UNKNOWN");
        assertNotNull(result);
        assertSame(smartFiltersResponseTransformerDesktop, result);
    }
}
