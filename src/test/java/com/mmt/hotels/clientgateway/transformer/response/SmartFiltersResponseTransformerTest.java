package com.mmt.hotels.clientgateway.transformer.response;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersResponse;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import com.mmt.hotels.clientgateway.response.filter.Filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SmartFiltersResponseTransformerTest {

    private TestSmartFiltersResponseTransformer transformer;

    @BeforeEach
    void setUp() {
        transformer = new TestSmartFiltersResponseTransformer();
    }

    @Test
    void testConvertSmartFiltersResponseWithValidFilters() {
        // Given
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        List<Filter> matchingFilters = new ArrayList<>();
        Filter filter1 = new Filter();
        filter1.setTitle("Test Filter 1");
        matchingFilters.add(filter1);

        Filter filter2 = new Filter();
        filter2.setTitle("Test Filter 2");
        matchingFilters.add(filter2);

        // When
        SmartFiltersResponse result = transformer.convertSmartFiltersResponse(
                matchingFilters, filterCountRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertEquals(2, result.getFilters().size());
        assertEquals("Test Filter 1", result.getFilters().get(0).getTitle());
        assertEquals("Test Filter 2", result.getFilters().get(1).getTitle());
    }

    @Test
    void testConvertSmartFiltersResponseWithEmptyFilters() {
        // Given
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        List<Filter> matchingFilters = new ArrayList<>();

        // When
        SmartFiltersResponse result = transformer.convertSmartFiltersResponse(
                matchingFilters, filterCountRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertTrue(result.getFilters().isEmpty());
    }

    @Test
    void testConvertSmartFiltersResponseWithNullFilters() {
        // Given
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        List<Filter> matchingFilters = null;

        // When
        SmartFiltersResponse result = transformer.convertSmartFiltersResponse(
                matchingFilters, filterCountRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertTrue(result.getFilters().isEmpty());
    }

    @Test
    void testApplyClientSpecificTransformations() {
        // Given
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        List<Filter> matchingFilters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setTitle("Test Filter");
        matchingFilters.add(filter);

        // When
        List<Filter> result = transformer.applyClientSpecificTransformations(
                matchingFilters, filterCountRequest);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Filter", result.get(0).getTitle());
    }

    @Test
    void testConvertSmartFiltersResponseWithFilterTags() {
        // Given
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        List<String> filterTags = Arrays.asList("Test Filter 1", "Test Filter 2");
        List<Filter> allFilters = new ArrayList<>();
        
        Filter filter1 = new Filter();
        filter1.setFilterValue("Test Filter 1");
        filter1.setTitle("Test Filter 1");
        allFilters.add(filter1);
        
        Filter filter2 = new Filter();
        filter2.setFilterValue("Test Filter 2");
        filter2.setTitle("Test Filter 2");
        allFilters.add(filter2);
        
        Filter filter3 = new Filter();
        filter3.setTitle("Test Filter 3");
        allFilters.add(filter3);

        // When
        SmartFiltersResponse result = transformer.convertSmartFiltersResponse(
                filterTags, allFilters, filterCountRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertEquals(2, result.getFilters().size());
        assertEquals("Test Filter 1", result.getFilters().get(0).getTitle());
        assertEquals("Test Filter 2", result.getFilters().get(1).getTitle());
    }

    @Test
    void testConvertSmartFiltersResponseWithEmptyFilterTags() {
        // Given
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        List<String> filterTags = new ArrayList<>();
        List<Filter> allFilters = new ArrayList<>();
        Filter filter = new Filter();
        filter.setTitle("Test Filter");
        allFilters.add(filter);

        // When
        SmartFiltersResponse result = transformer.convertSmartFiltersResponse(
                filterTags, allFilters, filterCountRequest);

        // Then
        assertNotNull(result);
        assertNotNull(result.getFilters());
        assertTrue(result.getFilters().isEmpty());
    }

    @Test
    void testGetMatchingFilters() {
        // Given
        List<String> filterTags = Arrays.asList("Test Filter 1", "Test Filter 2");
        List<Filter> allFilters = new ArrayList<>();
        
        Filter filter1 = new Filter();
        filter1.setTitle("Test Filter 1");
        filter1.setFilterGroup("GROUP1");
        allFilters.add(filter1);
        
        Filter filter2 = new Filter();
        filter2.setTitle("Test Filter 2");
        filter2.setFilterGroup("GROUP2");
        allFilters.add(filter2);
        
        Filter filter3 = new Filter();
        filter3.setTitle("Test Filter 3");
        filter3.setFilterGroup("GROUP3");
        allFilters.add(filter3);

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Test Filter 1", result.get(0).getTitle());
        assertEquals("Test Filter 2", result.get(1).getTitle());
    }

    @Test
    void testGetMatchingFiltersWithCustomTitles() {
        // Given
        List<String> filterTags = Arrays.asList("HOTEL_PRICE_BUCKET:Price Filter", "USER_RATING_GI_BRAND:Rating Filter");
        List<Filter> allFilters = new ArrayList<>();
        
        Filter priceFilter = new Filter();
        priceFilter.setTitle("Price Filter");
        priceFilter.setFilterGroup("HOTEL_PRICE_BUCKET");
        allFilters.add(priceFilter);
        
        Filter ratingFilter = new Filter();
        ratingFilter.setTitle("Rating Filter");
        ratingFilter.setFilterGroup("USER_RATING_GI_BRAND");
        allFilters.add(ratingFilter);
        
        Filter normalFilter = new Filter();
        normalFilter.setTitle("Normal Filter");
        normalFilter.setFilterGroup("NORMAL_GROUP");
        allFilters.add(normalFilter);

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Price Filter", result.get(0).getTitle());
        assertEquals("Rating Filter", result.get(1).getTitle());
    }

    @Test
    void testGetMatchingFiltersWithMixedTitles() {
        // Given
        List<String> filterTags = Arrays.asList("Normal Filter", "HOTEL_PRICE_BUCKET:Price Filter");
        List<Filter> allFilters = new ArrayList<>();
        
        Filter normalFilter = new Filter();
        normalFilter.setTitle("Normal Filter");
        normalFilter.setFilterGroup("NORMAL_GROUP");
        allFilters.add(normalFilter);
        
        Filter priceFilter = new Filter();
        priceFilter.setTitle("Price Filter");
        priceFilter.setFilterGroup("HOTEL_PRICE_BUCKET");
        allFilters.add(priceFilter);

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Normal Filter", result.get(0).getTitle());
        assertEquals("Price Filter", result.get(1).getTitle());
    }

    @Test
    void testGetMatchingFiltersWithEmptyInputs() {
        // Given
        List<String> filterTags = new ArrayList<>();
        List<Filter> allFilters = new ArrayList<>();

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testGetMatchingFiltersWithPriceFilterTag() {
        // Given
        List<String> filterTags = Arrays.asList("HOTEL_PRICE_BUCKET:₹1000 to ₹2000");
        List<Filter> allFilters = new ArrayList<>();
        
        Filter normalFilter = new Filter();
        normalFilter.setTitle("Normal Filter");
        normalFilter.setFilterGroup("NORMAL_GROUP");
        allFilters.add(normalFilter);

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        Filter priceFilter = result.get(0);
        assertEquals("HOTEL_PRICE_BUCKET", priceFilter.getFilterGroup());
        assertEquals("₹1000 to ₹2000", priceFilter.getTitle());
        assertNull(priceFilter.getFilterValue());
        assertNotNull(priceFilter.getFilterRange());
        assertEquals(1000, priceFilter.getFilterRange().getMinValue());
        assertEquals(2000, priceFilter.getFilterRange().getMaxValue());
    }

    @Test
    void testGetMatchingFiltersWithPriceFilterTagAndExistingFilter() {
        // Given
        List<String> filterTags = Arrays.asList("Normal Filter", "HOTEL_PRICE_BUCKET:₹500 to ₹1500");
        List<Filter> allFilters = new ArrayList<>();
        
        Filter normalFilter = new Filter();
        normalFilter.setTitle("Normal Filter");
        normalFilter.setFilterGroup("NORMAL_GROUP");
        allFilters.add(normalFilter);

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // Check normal filter
        Filter matchedNormalFilter = result.stream()
                .filter(f -> "NORMAL_GROUP".equals(f.getFilterGroup()))
                .findFirst().orElse(null);
        assertNotNull(matchedNormalFilter);
        assertEquals("Normal Filter", matchedNormalFilter.getTitle());
        
        // Check price filter
        Filter priceFilter = result.stream()
                .filter(f -> "HOTEL_PRICE_BUCKET".equals(f.getFilterGroup()))
                .findFirst().orElse(null);
        assertNotNull(priceFilter);
        assertEquals("₹500 to ₹1500", priceFilter.getTitle());
        assertEquals(500, priceFilter.getFilterRange().getMinValue());
        assertEquals(1500, priceFilter.getFilterRange().getMaxValue());
    }

    @Test
    void testGetMatchingFiltersWithDuplicatePriceFilterTag() {
        // Given
        List<String> filterTags = Arrays.asList("HOTEL_PRICE_BUCKET:₹1000 to ₹2000", "HOTEL_PRICE_BUCKET:₹1000 to ₹2000");
        List<Filter> allFilters = new ArrayList<>();

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size()); // Should not add duplicate
        Filter priceFilter = result.get(0);
        assertEquals("HOTEL_PRICE_BUCKET", priceFilter.getFilterGroup());
        assertEquals("₹1000 to ₹2000", priceFilter.getTitle());
    }

    @Test
    void testGetMatchingFiltersWithInvalidPriceFilterTag() {
        // Given
        List<String> filterTags = Arrays.asList("HOTEL_PRICE_BUCKET:Invalid Format");
        List<Filter> allFilters = new ArrayList<>();

        // When
        List<Filter> result = transformer.getMatchingFilters(allFilters, filterTags);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size()); // Should not add invalid price filter
    }

    // Test implementation of the abstract class
    private static class TestSmartFiltersResponseTransformer extends SmartFiltersResponseTransformer {
        // This class is used for testing the abstract class functionality
    }
}
