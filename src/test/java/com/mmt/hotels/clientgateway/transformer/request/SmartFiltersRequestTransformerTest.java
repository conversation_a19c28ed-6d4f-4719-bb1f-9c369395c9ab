package com.mmt.hotels.clientgateway.transformer.request;

import com.mmt.hotels.clientgateway.businessobjects.SmartFiltersRequest;
import com.mmt.hotels.clientgateway.constants.Constants;
import com.mmt.hotels.clientgateway.request.FilterCountRequest;
import com.mmt.hotels.clientgateway.request.SearchHotelsCriteria;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class SmartFiltersRequestTransformerTest {

    @Test
    public void testConvertSmartFiltersRequest() {
        // Create a test implementation
        SmartFiltersRequestTransformer transformer = new SmartFiltersRequestTransformer() {
            @Override
            protected void applyClientSpecificTransformations(
                    SmartFiltersRequest smartFiltersRequest, 
                    FilterCountRequest filterCountRequest, 
                    List<String> filterTitles) {
                // No additional transformations for this test
            }
        };

        // Create test data
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("hotels in mumbai");
        filterCountRequest.setSearchCriteria(searchCriteria);

        // Create test filters
        List<com.mmt.hotels.clientgateway.response.filter.Filter> allFilters = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
        filter1.setTitle("5 Star");
        allFilters.add(filter1);
        
        com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
        filter2.setTitle("Budget");
        allFilters.add(filter2);

        // Execute transformation
        SmartFiltersRequest result = transformer.convertSmartFiltersRequest(filterCountRequest, allFilters);

        // Verify results
        assertNotNull(result);
        assertEquals("hotels in mumbai", result.getQueryText());
        assertEquals(Arrays.asList("5 Star", "Budget"), result.getFilters());
    }

    @Test
    public void testConvertSmartFiltersRequestWithNullQueryText() {
        // Create a test implementation
        SmartFiltersRequestTransformer transformer = new SmartFiltersRequestTransformer() {
            @Override
            protected void applyClientSpecificTransformations(
                    SmartFiltersRequest smartFiltersRequest, 
                    FilterCountRequest filterCountRequest, 
                    List<String> filterTitles) {
                // No additional transformations for this test
            }
        };

        // Create test data with null query text
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText(null);
        filterCountRequest.setSearchCriteria(searchCriteria);

        // Create test filters
        List<com.mmt.hotels.clientgateway.response.filter.Filter> allFilters = new ArrayList<>();
        com.mmt.hotels.clientgateway.response.filter.Filter filter1 = new com.mmt.hotels.clientgateway.response.filter.Filter();
        filter1.setTitle("5 Star");
        allFilters.add(filter1);
        
        com.mmt.hotels.clientgateway.response.filter.Filter filter2 = new com.mmt.hotels.clientgateway.response.filter.Filter();
        filter2.setTitle("Budget");
        allFilters.add(filter2);

        // Execute transformation
        SmartFiltersRequest result = transformer.convertSmartFiltersRequest(filterCountRequest, allFilters);

        // Verify results
        assertNotNull(result);
        assertNull(result.getQueryText());
        assertEquals(Arrays.asList("5 Star", "Budget"), result.getFilters());
    }

    @Test
    public void testConvertSmartFiltersRequestWithEmptyFilterTitles() {
        // Create a test implementation
        SmartFiltersRequestTransformer transformer = new SmartFiltersRequestTransformer() {
            @Override
            protected void applyClientSpecificTransformations(
                    SmartFiltersRequest smartFiltersRequest, 
                    FilterCountRequest filterCountRequest, 
                    List<String> filterTitles) {
                // No additional transformations for this test
            }
        };

        // Create test data with empty filter list
        FilterCountRequest filterCountRequest = new FilterCountRequest();
        SearchHotelsCriteria searchCriteria = new SearchHotelsCriteria();
        searchCriteria.setSearchQueryText("test query");
        filterCountRequest.setSearchCriteria(searchCriteria);

        List<com.mmt.hotels.clientgateway.response.filter.Filter> allFilters = Arrays.asList();

        // Execute transformation
        SmartFiltersRequest result = transformer.convertSmartFiltersRequest(filterCountRequest, allFilters);

        // Verify results
        assertNotNull(result);
        assertEquals("test query", result.getQueryText());
        assertEquals(0, result.getFilters().size());
    }

    @Test
    public void testGetTitlesFromFiltersWithCustomLogic() {
        // Create a test implementation
        SmartFiltersRequestTransformer transformer = new SmartFiltersRequestTransformer() {
            @Override
            protected void applyClientSpecificTransformations(
                    SmartFiltersRequest smartFiltersRequest, 
                    FilterCountRequest filterCountRequest, 
                    List<String> filterTitles) {
                // No additional transformations for this test
            }
        };

        // Create test filters
        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
        
        // Price filter (should get custom title)
        com.mmt.hotels.clientgateway.response.filter.Filter priceFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        priceFilter.setFilterGroup(Constants.FILTER_PRICE_BUCKET_HES);
        priceFilter.setTitle("₹1000-2000");
        filters.add(priceFilter);
        
        // User rating filter (should get custom title)
        com.mmt.hotels.clientgateway.response.filter.Filter ratingFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        ratingFilter.setFilterGroup(Constants.FILTER_USER_RATING_GI_BRAND);
        ratingFilter.setTitle("4+ Stars");
        filters.add(ratingFilter);
        
        // Regular filter (should not get custom title)
        com.mmt.hotels.clientgateway.response.filter.Filter regularFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        regularFilter.setFilterGroup("AMENITY");
        regularFilter.setTitle("Free WiFi");
        filters.add(regularFilter);

        // Execute transformation
        List<String> titles = transformer.getTitlesFromFilters(filters);

        // Verify results
        assertNotNull(titles);
        assertEquals(3, titles.size());
        assertEquals(Constants.FILTER_PRICE_BUCKET_HES + ":₹1000-2000", titles.get(0));
        assertEquals(Constants.FILTER_USER_RATING_GI_BRAND + ":4+ Stars", titles.get(1));
        assertEquals("Free WiFi", titles.get(2));
    }

    @Test
    public void testGetTitlesFromFiltersWithNewFilterGroup() {
        // This test demonstrates how easy it is to add new filter groups
        // by just adding them to the CUSTOM_TITLE_FILTER_GROUPS constant
        
        // Create a test implementation
        SmartFiltersRequestTransformer transformer = new SmartFiltersRequestTransformer() {
            @Override
            protected void applyClientSpecificTransformations(
                    SmartFiltersRequest smartFiltersRequest, 
                    FilterCountRequest filterCountRequest, 
                    List<String> filterTitles) {
                // No additional transformations for this test
            }
        };

        // Create test filters
        List<com.mmt.hotels.clientgateway.response.filter.Filter> filters = new ArrayList<>();
        
        // Existing custom title filter
        com.mmt.hotels.clientgateway.response.filter.Filter priceFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        priceFilter.setFilterGroup(Constants.FILTER_PRICE_BUCKET_HES);
        priceFilter.setTitle("₹2000-3000");
        filters.add(priceFilter);
        
        // Regular filter (not in custom list)
        com.mmt.hotels.clientgateway.response.filter.Filter regularFilter = new com.mmt.hotels.clientgateway.response.filter.Filter();
        regularFilter.setFilterGroup("LOCATION");
        regularFilter.setTitle("City Center");
        filters.add(regularFilter);

        // Execute transformation
        List<String> titles = transformer.getTitlesFromFilters(filters);

        // Verify results
        assertNotNull(titles);
        assertEquals(2, titles.size());
        assertEquals(Constants.FILTER_PRICE_BUCKET_HES + ":₹2000-3000", titles.get(0));
        assertEquals("City Center", titles.get(1));
    }
}
